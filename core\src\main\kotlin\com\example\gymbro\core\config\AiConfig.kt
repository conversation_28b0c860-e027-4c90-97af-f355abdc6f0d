package com.example.gymbro.core.config

/**
 * AI相关配置统一管理
 * 
 * 🧹 HARDCODE CLEANUP: 统一管理所有AI相关的硬编码配置值
 * 
 * 设计原则：
 * - 集中管理AI请求、超时、限流等配置
 * - 支持不同AI服务的配置
 * - 提供类型安全的配置访问
 * - 便于性能调优和监控
 */
object AiConfig {
    
    /**
     * 请求超时配置
     */
    object Timeouts {
        /**
         * AI请求默认超时时间 (毫秒)
         * 用于一般的AI对话请求
         */
        const val DEFAULT_REQUEST_TIMEOUT = 10_000L // 10秒
        
        /**
         * 长时间AI请求超时 (毫秒)
         * 用于复杂的AI分析和生成任务
         */
        const val LONG_REQUEST_TIMEOUT = 30_000L // 30秒
        
        /**
         * 流式响应超时 (毫秒)
         * 用于实时流式AI响应
         */
        const val STREAMING_TIMEOUT = 60_000L // 60秒
        
        /**
         * 连接建立超时 (毫秒)
         */
        const val CONNECT_TIMEOUT = 5_000L // 5秒
        
        /**
         * 读取超时 (毫秒)
         */
        const val READ_TIMEOUT = 30_000L // 30秒
    }
    
    /**
     * Token限制配置
     */
    object Tokens {
        /**
         * 系统每日Token限制
         */
        const val DAILY_TOKEN_LIMIT = 1_000_000 // 100万tokens
        
        /**
         * 单次请求最大Token数
         */
        const val MAX_TOKENS_PER_REQUEST = 4_000
        
        /**
         * 上下文窗口大小
         */
        const val CONTEXT_WINDOW_SIZE = 8_000
        
        /**
         * 响应最大Token数
         */
        const val MAX_RESPONSE_TOKENS = 2_000
        
        /**
         * Token使用警告阈值 (百分比)
         */
        const val WARNING_THRESHOLD = 80 // 80%
        
        /**
         * Token使用限制阈值 (百分比)
         */
        const val LIMIT_THRESHOLD = 95 // 95%
    }
    
    /**
     * 熔断器配置
     */
    object CircuitBreaker {
        /**
         * 失败阈值
         * 连续失败多少次后触发熔断
         */
        const val FAILURE_THRESHOLD = 5
        
        /**
         * 熔断超时时间 (毫秒)
         * 熔断后多长时间尝试恢复
         */
        const val TIMEOUT_DURATION = 60_000L // 1分钟
        
        /**
         * 半开状态下的测试请求数
         */
        const val TEST_REQUEST_COUNT = 3
        
        /**
         * 成功率阈值 (百分比)
         * 低于此成功率时触发熔断
         */
        const val SUCCESS_RATE_THRESHOLD = 50 // 50%
        
        /**
         * 统计窗口大小 (请求数)
         */
        const val STATISTICS_WINDOW_SIZE = 100
    }
    
    /**
     * 重试配置
     */
    object Retry {
        /**
         * 默认重试次数
         */
        const val DEFAULT_RETRY_COUNT = 3
        
        /**
         * 重试延迟 (毫秒)
         */
        const val RETRY_DELAY = 1_000L // 1秒
        
        /**
         * 最大重试延迟 (毫秒)
         */
        const val MAX_RETRY_DELAY = 10_000L // 10秒
        
        /**
         * 指数退避因子
         */
        const val BACKOFF_MULTIPLIER = 2.0
        
        /**
         * 抖动因子 (0.0-1.0)
         * 用于避免重试风暴
         */
        const val JITTER_FACTOR = 0.1
    }
    
    /**
     * 缓存配置
     */
    object Cache {
        /**
         * 响应缓存时间 (毫秒)
         */
        const val RESPONSE_CACHE_DURATION = 5 * 60 * 1000L // 5分钟
        
        /**
         * 最大缓存条目数
         */
        const val MAX_CACHE_ENTRIES = 100
        
        /**
         * 缓存键过期时间 (毫秒)
         */
        const val CACHE_KEY_EXPIRY = 24 * 60 * 60 * 1000L // 24小时
    }
    
    /**
     * 流式处理配置
     */
    object Streaming {
        /**
         * 流式缓冲区大小
         */
        const val BUFFER_SIZE = 64 // 64个token
        
        /**
         * 流式处理延迟 (毫秒)
         * 用于控制token发送频率
         */
        const val PROCESSING_DELAY = 50L // 50ms
        
        /**
         * 最大并发流数
         */
        const val MAX_CONCURRENT_STREAMS = 5
        
        /**
         * 流式连接保活时间 (毫秒)
         */
        const val KEEP_ALIVE_DURATION = 30_000L // 30秒
    }
    
    /**
     * 监控配置
     */
    object Monitoring {
        /**
         * 性能指标采样率 (0.0-1.0)
         */
        const val METRICS_SAMPLE_RATE = 0.1 // 10%
        
        /**
         * 慢请求阈值 (毫秒)
         */
        const val SLOW_REQUEST_THRESHOLD = 5_000L // 5秒
        
        /**
         * 错误率警告阈值 (百分比)
         */
        const val ERROR_RATE_WARNING = 10 // 10%
        
        /**
         * 统计窗口时间 (毫秒)
         */
        const val STATISTICS_WINDOW_DURATION = 60_000L // 1分钟
    }
}
