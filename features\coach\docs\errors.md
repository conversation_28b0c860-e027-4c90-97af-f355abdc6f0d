> Task :features:coach:compileDebugKotlin FAILED
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/AiCoachScreen.kt:51:47 Unresolved reference 'HeightExtraLarge'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/components/ChatInterface.kt:349:21 No parameter with name 'sessionId' found.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/AiCoachEffectHandler.kt:1150:42 Class '<anonymous>' is not abstract and does not implement abstract member 'onDisplayComplete'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/AiCoachEffectHandler.kt:1151:21 'onDisplayComplete' overrides nothing. Potential signatures for overriding:
fun onDisplayComplete(messageId: String, thinkingProcess: String, finalContent: String, metadata: Map<String, Any> = ...): Unit
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/AiCoachEffectHandler.kt:1169:21 'onDisplayError' overrides nothing. Potential signatures for overriding:
fun onDisplayError(messageId: String, error: Throwable, partialResult: String? = ...): Unit
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/handlers/StreamEffectHandler.kt:13:52 Unresolved reference 'ConversationTurn'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/handlers/StreamEffectHandler.kt:14:52 Unresolved reference 'ThinkingBoxContext'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/handlers/StreamEffectHandler.kt:15:52 Unresolved reference 'UserProfileContext'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/handlers/StreamEffectHandler.kt:131:8 Unresolved reference 'ThinkingBoxContext'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/handlers/StreamEffectHandler.kt:149:21 Unresolved reference 'ThinkingBoxContext'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/handlers/StreamEffectHandler.kt:150:39 Unresolved reference 'UserProfileContext'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/handlers/StreamEffectHandler.kt:154:44 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/effect/handlers/StreamEffectHandler.kt:159:37 Unresolved reference 'ConversationTurn'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/service/CoachCompletionListenerImpl.kt:28:1 Class 'CoachCompletionListenerImpl' is not abstract and does not implement abstract member 'onDisplayComplete'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/service/CoachCompletionListenerImpl.kt:35:5 'onDisplayComplete' overrides nothing. Potential signatures for overriding:
fun onDisplayComplete(messageId: String, thinkingProcess: String, finalContent: String, metadata: Map<String, Any> = ...): Unit
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/service/CoachCompletionListenerImpl.kt:77:5 'onDisplayError' overrides nothing. Potential signatures for overriding:
fun onDisplayError(messageId: String, error: Throwable, partialResult: String? = ...): Unit
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/service/CoachCompletionListenerImpl.kt:172:60 Unresolved reference 'ThinkingBoxError'.
e: file:///D:/GymBro/GymBro/features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/internal/service/CoachCompletionListenerImpl.kt:178:48 Unresolved reference 'message'.

