package com.example.gymbro.features.workout.shared.components.keypad

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors

/**
 * 按钮类型枚举
 */
enum class KeypadButtonType {
    NUMBER, // 数字按钮
    FUNCTION, // 功能按钮
    ACTION, // 动作按钮
    DELETE, // 删除按钮
    NORMAL, // 普通按钮
}

/**
 * 计算器按键组件
 *
 * 支持文本和图标两种模式，集成动画效果和触觉反馈
 * 遵循Material3设计规范和GymBro设计系统
 */
@Composable
fun KeypadButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    text: String? = null,
    icon: ImageVector? = null,
    isHighlighted: Boolean = false,
    isEnabled: Boolean = true,
    backgroundColor: Color? = null,
    contentColor: Color? = null,
    buttonType: KeypadButtonType = KeypadButtonType.NORMAL,
) {
    val hapticFeedback = LocalHapticFeedback.current
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    // 动画效果
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = tween(100),
        label = "button_scale",
    )

    // 背景色动画
    val animatedBackgroundColor by animateColorAsState(
        targetValue = when {
            !isEnabled -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.3f)
            backgroundColor != null -> backgroundColor
            isHighlighted -> MaterialTheme.workoutColors.accentPrimary
            isPressed -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.8f)
            else -> when (buttonType) {
                KeypadButtonType.NUMBER -> MaterialTheme.colorScheme.surface
                KeypadButtonType.FUNCTION -> MaterialTheme.workoutColors.cardBackground
                KeypadButtonType.ACTION -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f)
                // 🧹 HARDCODE CLEANUP: 使用主题颜色替代硬编码颜色
                KeypadButtonType.DELETE -> MaterialTheme.workoutColors.errorPrimary.copy(alpha = 0.1f)
                KeypadButtonType.NORMAL -> MaterialTheme.colorScheme.surface
            }
        },
        animationSpec = tween(150),
        label = "background_color",
    )

    // 内容色动画
    val animatedContentColor by animateColorAsState(
        targetValue = when {
            !isEnabled -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.5f)
            contentColor != null -> contentColor
            isHighlighted -> MaterialTheme.colorScheme.onPrimary
            else -> when (buttonType) {
                KeypadButtonType.NUMBER -> MaterialTheme.workoutColors.accentSecondary
                KeypadButtonType.FUNCTION -> MaterialTheme.workoutColors.accentPrimary
                KeypadButtonType.ACTION -> MaterialTheme.workoutColors.accentPrimary
                // 🧹 HARDCODE CLEANUP: 使用主题颜色替代硬编码颜色
                KeypadButtonType.DELETE -> MaterialTheme.workoutColors.errorPrimary
                KeypadButtonType.NORMAL -> MaterialTheme.workoutColors.accentSecondary
            }
        },
        animationSpec = tween(150),
        label = "content_color",
    )

    Surface(
        modifier = modifier
            .size(48.dp)
            .scale(scale)
            .clip(RoundedCornerShape(Tokens.Radius.Small))
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                enabled = isEnabled,
            ) {
                hapticFeedback.performHapticFeedback(
                    when (buttonType) {
                        KeypadButtonType.ACTION, KeypadButtonType.DELETE -> HapticFeedbackType.LongPress
                        else -> HapticFeedbackType.TextHandleMove
                    },
                )
                onClick()
            },
        color = animatedBackgroundColor,
        shape = RoundedCornerShape(Tokens.Radius.Small),
        shadowElevation = if (isPressed) 0.dp else 2.dp,
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            when {
                text != null -> {
                    Text(
                        text = text,
                        style = MaterialTheme.typography.bodyLarge.copy(
                            fontWeight = when (buttonType) {
                                KeypadButtonType.NUMBER -> FontWeight.Medium
                                KeypadButtonType.FUNCTION -> FontWeight.Bold
                                KeypadButtonType.ACTION -> FontWeight.Bold
                                KeypadButtonType.DELETE -> FontWeight.Bold
                                KeypadButtonType.NORMAL -> FontWeight.Medium
                            },
                        ),
                        color = animatedContentColor,
                    )
                }
                icon != null -> {
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        tint = animatedContentColor,
                        modifier = Modifier.size(20.dp),
                    )
                }
            }
        }
    }
}

/**
 * 数字按键组件
 */
@Composable
fun NumberKeypadButton(
    number: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isEnabled: Boolean = true,
) {
    KeypadButton(
        text = number.toString(),
        onClick = onClick,
        modifier = modifier,
        isEnabled = isEnabled,
        buttonType = KeypadButtonType.NUMBER,
    )
}

/**
 * 功能按键组件
 */
@Composable
fun FunctionKeypadButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isHighlighted: Boolean = false,
    isEnabled: Boolean = true,
    isAction: Boolean = false,
) {
    KeypadButton(
        text = text,
        onClick = onClick,
        modifier = modifier,
        isHighlighted = isHighlighted,
        isEnabled = isEnabled,
        buttonType = if (isAction) KeypadButtonType.ACTION else KeypadButtonType.FUNCTION,
    )
}

/**
 * 图标按键组件
 */
@Composable
fun IconKeypadButton(
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isHighlighted: Boolean = false,
    isEnabled: Boolean = true,
    buttonType: KeypadButtonType = KeypadButtonType.NORMAL,
) {
    KeypadButton(
        icon = icon,
        onClick = onClick,
        modifier = modifier,
        isHighlighted = isHighlighted,
        isEnabled = isEnabled,
        buttonType = buttonType,
    )
}
