package com.example.gymbro.di.network

// --- Data Connect Imports ---
// 移除了 connectors.default.DefaultService 导入
// --- End Data Connect Imports ---
import android.content.Context
// 🧹 HARDCODE CLEANUP: 使用统一配置管理系统
import com.example.gymbro.core.config.NetworkConfig
import com.example.gymbro.data.exercise.remote.ExerciseApi
import com.example.gymbro.data.remote.WorkoutApi
// 移除不存在的GymBroApi导入
// import com.example.gymbro.data.remote.api.GymBroApi
import com.example.gymbro.data.ai.api.IpEchoService
import com.example.gymbro.data.ai.api.PricingApi

// 删除已删除的IpLocationApi导入
// import com.example.gymbro.data.remote.api.IpLocationApi

import com.example.gymbro.di.core.PricingRetrofit
// 移除错误的AndroidNetworkMonitor导入，因为di模块无法访问app模块
// import com.example.gymbro.network.AndroidNetworkMonitor
import com.google.gson.Gson
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.Cache
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton

/**
 * 网络服务提供模块
 * 提供网络相关的依赖，包括Retrofit、OkHttp客户端和API服务实例
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkServiceModule {
    // 🧹 HARDCODE CLEANUP: 所有配置值已移至NetworkConfig统一管理
    // 使用NetworkConfig.Api.BASE_URL等替代硬编码值

    // --- Data Connect Constants ---
    // 这些常量目前未使用，但保留以备将来需要
    private const val LOCAL_CONNECTOR_PORT = 9399
    private const val LOCAL_CONNECTOR_URL = "http://10.0.2.2:${LOCAL_CONNECTOR_PORT}" // 10.0.2.2 用于 Android 模拟器访问宿主机
    private const val IS_DEBUG = true // 添加一个调试模式常量作为替代
    // --- End Data Connect Constants ---

    /**
     * 提供OkHttp缓存
     */
    @Provides
    @Singleton
    fun provideOkHttpCache(
        @ApplicationContext context: Context,
    ): Cache = Cache(context.cacheDir, NetworkConfig.Cache.HTTP_CACHE_SIZE)

    /**
     * 提供Gson实例
     */
    @Provides
    @Singleton
    fun provideGson(): Gson = Gson()

    /**
     * 提供日志拦截器
     * 🔒 安全修复：防止敏感信息泄漏
     */
    @Provides
    @Singleton
    fun provideLoggingInterceptor(): HttpLoggingInterceptor =
        HttpLoggingInterceptor().apply {
            // 使用Timber的可调试状态检测（更通用的方式）
            val isDebuggable = Timber.treeCount > 0
            if (isDebuggable) {
                level = HttpLoggingInterceptor.Level.BODY
                // ★ 防止敏感信息泄漏：隐藏Authorization头部
                redactHeader("Authorization")
            } else {
                level = HttpLoggingInterceptor.Level.BASIC
            }
        }

    // 🧹 REMOVED: Legacy OkHttpClient已完全迁移到CoreNetworkModule
    // 所有网络请求现在使用@Named("rest_client")或@Named("ws_client")

    /**
     * 提供IP验证专用的快速OkHttpClient实例
     * 优化超时时间，确保在Loading页面显示期间完成IP验证
     */
    @Provides
    @Singleton
    @Named("ip_client")
    fun provideIpOkHttpClient(
        loggingInterceptor: HttpLoggingInterceptor,
    ): OkHttpClient =
        OkHttpClient
            .Builder()
            .connectTimeout(NetworkConfig.Timeouts.IP_CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(NetworkConfig.Timeouts.IP_READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(NetworkConfig.Timeouts.IP_READ_TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(loggingInterceptor)
            .build()

    /**
     * 提供主要业务API的Retrofit实例
     * 🔧 使用CoreNetworkModule的统一REST客户端
     */
    @Provides
    @Singleton
    fun provideRetrofit(
        @Named("rest_client") okHttpClient: OkHttpClient,
        gson: Gson,
    ): Retrofit =
        Retrofit
            .Builder()
            .baseUrl(NetworkConfig.Api.BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()

    /**
     * 提供 WorkoutApi 服务实例
     */
    @Provides
    @Singleton
    fun provideWorkoutApi(retrofit: Retrofit): WorkoutApi = retrofit.create(WorkoutApi::class.java)

    /**
     * 提供 ExerciseApi 服务实例
     */
    @Provides
    @Singleton
    fun provideExerciseApi(retrofit: Retrofit): ExerciseApi = retrofit.create(ExerciseApi::class.java)

    /**
     * 提供定价服务的Retrofit实例
     * 🔧 使用CoreNetworkModule的统一REST客户端
     */
    @Provides
    @Singleton
    @PricingRetrofit
    fun providePricingRetrofit(
        @Named("rest_client") okHttpClient: OkHttpClient,
        gson: Gson,
    ): Retrofit =
        Retrofit
            .Builder()
            .baseUrl("https://pricing.gymbro.app/")
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()

    /**
     * 提供定价服务API接口
     */
    @Provides
    @Singleton
    fun providePricingApi(
        @PricingRetrofit retrofit: Retrofit,
    ): PricingApi = retrofit.create(PricingApi::class.java)

    /**
     * 提供IP回显服务
     * 使用快速客户端优化IP获取性能
     */
    @Provides
    @Singleton
    fun provideIpEchoService(
        @Named("ip_client") okHttpClient: OkHttpClient,
    ): IpEchoService =
        Retrofit
            .Builder()
            .baseUrl("https://api.ipify.org/")
            .client(okHttpClient)
            .addConverterFactory(ScalarsConverterFactory.create())
            .build()
            .create(IpEchoService::class.java)

    // 从DataSourceModule迁移的接口，已整合到本模块中
}

/**
 * 网络监控绑定模块
 *
 * 🧹 REMOVED: NetworkMonitor绑定已迁移到CoreNetworkModule
 * 避免重复绑定，统一使用core-network模块的实现
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class NetworkMonitorModule {
    // 🧹 REMOVED: NetworkMonitor绑定已迁移到CoreNetworkModule
    // 统一使用 com.example.gymbro.core.network.monitor.AndroidNetworkMonitor
}
