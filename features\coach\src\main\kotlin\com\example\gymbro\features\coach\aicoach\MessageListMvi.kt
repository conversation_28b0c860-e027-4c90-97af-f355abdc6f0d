package com.example.gymbro.features.coach.aicoach

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
// 🧹 ARCHITECTURE FIX: 直接使用ThinkingBox模块的统一渲染器
import com.example.gymbro.features.thinkingbox.ThinkingBoxStaticRenderer
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import java.util.*

// ===== MVI Contract =====

/**
 * MessageList组件的MVI Contract
 * 基于Profile模块成功实践的Contract模式
 */
object MessageListContract {
    /**
     * Intent - 用户意图
     */
    sealed interface Intent {
        /** 重试发送消息 */
        data class RetryMessage(
            val messageId: String,
        ) : Intent

        /** 复制消息内容 */
        data class CopyMessage(
            val messageId: String,
            val content: String,
        ) : Intent

        /** 滚动到底部 */
        data object ScrollToBottom : Intent
    }

    /**
     * State - 消息列表状态（由CoachContract.State派生）
     */
    internal data class State(
        val messages: ImmutableList<AiCoachContract.MessageUi> = persistentListOf(),
        val isLoading: Boolean = false,
        val isStreaming: Boolean = false,
        val autoScrollEnabled: Boolean = true,
    )

    /**
     * Effect - 一次性副作用
     */
    sealed interface Effect {
        /** 滚动到列表底部 */
        data object ScrollToBottom : Effect

        /** 复制到剪贴板 */
        data class CopyToClipboard(
            val content: String,
        ) : Effect
    }
}

// ===== MVI Composable =====

/**
 * MessageListMvi 组件 - 基于Profile模块成功实践的MVI化实现
 *
 * 设计原则：
 * 1. 完全由外部State驱动，遵循Profile模块模式
 * 2. 只处理消息显示逻辑，单一职责
 * 3. 将用户操作转换为Intent向上传递
 * 4. 集成StreamingText处理AI流式响应
 * 5. 使用稳定的Lambda参数，避免不必要的重组
 * 6. 全面集成主题系统
 *
 * @param messages 消息列表（不可变列表）
 * @param isStreaming 是否正在流式传输
 * @param modifier 修饰符
 * @param onIntent Intent回调（稳定Lambda）
 * @param onShowSummaryCard 点击历史消息摘要时的回调
 */
@Composable
internal fun MessageListMvi(
    messages: ImmutableList<AiCoachContract.MessageUi>,
    isStreaming: Boolean,
    modifier: Modifier = Modifier,
    onIntent: (MessageListContract.Intent) -> Unit = {},
    onShowSummaryCard: (String) -> Unit = {}, // 🔥 新增回调，用于显示历史思考过程
) {
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()

    // 🔍 添加 UI 组件调试日志
    LaunchedEffect(messages.size) {
        timber.log.Timber.tag("UI-DEBUG").d("🔍 [UI-DEBUG] MessageListMvi 接收到消息: ${messages.size}条")
        messages.forEachIndexed { index, msg ->
            timber.log.Timber.tag(
                "UI-DEBUG",
            ).d(
                "🔍 [UI-DEBUG] UI消息[$index]: id=${msg.id}, isFromUser=${msg.isFromUser}, content=${msg.content.take(
                    50,
                )}...",
            )
        }

        // 🔥 修复：检查key重复问题
        val keys = messages.map { message ->
            "msg_${message.id}_${message.timestamp}_${if (message.isFromUser) "user" else "ai"}"
        }
        val duplicateKeys = keys.groupingBy { it }.eachCount().filter { it.value > 1 }
        if (duplicateKeys.isNotEmpty()) {
            timber.log.Timber
                .tag("HISTORY-FIX")
                .e("❌ [Key重复检测] MessageListMvi发现重复keys: $duplicateKeys")
            timber.log.Timber
                .tag("HISTORY-FIX")
                .e(
                    "❌ [Key重复检测] 消息列表: ${messages.map { "${it.id}_${it.timestamp}_${if (it.isFromUser) "user" else "ai"}" }}",
                )
        } else {
            timber.log.Timber
                .tag("HISTORY-FIX")
                .d("✅ [Key重复检测] MessageListMvi所有keys唯一，共${keys.size}个")
        }
    }

    // 自动滚动到底部
    LaunchedEffect(messages.size, isStreaming) {
        if (messages.isNotEmpty()) {
            timber.log.Timber.tag("UI-DEBUG").d("🔍 [UI-DEBUG] 自动滚动到底部: ${messages.size}条消息")
            coroutineScope.launch {
                listState.animateScrollToItem(messages.size - 1)
            }
        } else {
            timber.log.Timber.tag("UI-DEBUG").d("🔍 [UI-DEBUG] 消息列表为空，不滚动")
        }
    }

    LazyColumn(
        modifier = modifier
            .fillMaxWidth(),
        state = listState,
        contentPadding = PaddingValues(Tokens.Spacing.Medium),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
    ) {
        items(
            items = messages,
            key = { message ->
                // 🔥 修复：确保key的绝对唯一性，防止历史记录加载时的key冲突
                "msg_${message.id}_${message.timestamp}_${if (message.isFromUser) "user" else "ai"}"
            },
        ) { message ->
            MessageBubbleMvi(
                message = message,
                onRetry = { onIntent(MessageListContract.Intent.RetryMessage(message.id)) },
                onCopy = { content -> onIntent(MessageListContract.Intent.CopyMessage(message.id, content)) },
                onShowSummaryCard = onShowSummaryCard, // 🔥 传递回调
            )
        }

        // 流式响应时的额外间距
        if (isStreaming) {
            item {
                Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))
            }
        }
    }
}

/**
 * 单个消息气泡MVI组件
 */
@Composable
private fun MessageBubbleMvi(
    message: AiCoachContract.MessageUi,
    onRetry: () -> Unit = {},
    onCopy: (String) -> Unit = {},
    onShowSummaryCard: (String) -> Unit = {}, // 🔥 新增回调
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = if (message.isFromUser) Arrangement.End else Arrangement.Start,
    ) {
        if (!message.isFromUser) {
            // AI头像
            Box(
                modifier =
                Modifier
                    .size(Tokens.Icon.Large)
                    .padding(end = Tokens.Spacing.Small),
                contentAlignment = Alignment.Center,
            ) {
                Surface(
                    shape = RoundedCornerShape(Tokens.Radius.Medium),
                    color = MaterialTheme.colorScheme.primary,
                ) {
                    Box(
                        modifier = Modifier.size(Tokens.Icon.Large),
                        contentAlignment = Alignment.Center,
                    ) {
                        Text(
                            text = "AI",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onPrimary,
                            fontSize = Tokens.Typography.Tiny,
                            fontWeight = FontWeight.Bold,
                        )
                    }
                }
            }
        }

        // 🔥 【修复】区分用户和AI消息的布局方式
        if (message.isFromUser) {
            // 用户消息使用气泡布局
            Surface(
                modifier = Modifier
                    .widthIn(max = 280.dp)
                    .padding(
                        start = Tokens.Icon.XXLarge,
                        end = 0.dp,
                    ),
                shape = RoundedCornerShape(
                    topStart = Tokens.Radius.Medium,
                    topEnd = Tokens.Radius.Medium,
                    bottomStart = Tokens.Radius.Medium,
                    bottomEnd = Tokens.Radius.XSmall,
                ),
                color = MaterialTheme.colorScheme.primaryContainer,
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Small),
                ) {
                    Text(
                        text = message.content,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                    )
                }
            }
        } else {
            // 🧹 ARCHITECTURE FIX: 直接使用ThinkingBox模块的统一渲染器
            val finalContent = message.finalMarkdown?.takeIf { it.isNotBlank() } ?: message.content
            if (finalContent.isNotBlank()) {
                ThinkingBoxStaticRenderer(
                    finalMarkdown = finalContent,
                    modifier = Modifier.fillMaxWidth(), // 🔥 AI消息全宽显示
                )
            }
        }

        if (message.isFromUser) {
            // 用户头像占位
            Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
        }
    }
}

// ===== Preview =====

@GymBroPreview
@Composable
private fun MessageListMviPreview() {
    GymBroTheme {
        val sampleMessages =
            persistentListOf(
                AiCoachContract.MessageUi(
                    id = "1",
                    content = "你好，我想了解一下如何制定健身计划",
                    isFromUser = true,
                    timestamp = Clock.System.now(),
                ),
                AiCoachContract.MessageUi(
                    id = "2",
                    content = "您好！制定个性化健身计划需要考虑以下几个关键因素...",
                    isFromUser = false,
                    timestamp = Clock.System.now(),
                    finalMarkdown = "这是一个示例的思考过程内容",
                ),
            )

        MessageListMvi(
            messages = sampleMessages,
            isStreaming = true,
        )
    }
}

@GymBroPreview
@Composable
private fun MessageBubbleMviPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            // 用户消息
            MessageBubbleMvi(
                message =
                AiCoachContract.MessageUi(
                    id = "1",
                    content = "这是一条用户消息",
                    isFromUser = true,
                    timestamp = Clock.System.now(),
                ),
            )

            // AI消息
            MessageBubbleMvi(
                message =
                AiCoachContract.MessageUi(
                    id = "2",
                    content = "这是一条AI回复消息，展示了最终渲染效果",
                    isFromUser = false,
                    timestamp = Clock.System.now(),
                    finalMarkdown = "这里是思考过程的内容",
                ),
            )
        }
    }
}
