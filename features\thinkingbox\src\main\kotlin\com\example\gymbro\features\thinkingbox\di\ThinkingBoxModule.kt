package com.example.gymbro.features.thinkingbox.di

// 🧹 ARCHITECTURE FIX: 移除跨层直接调用，通过Domain层接口注入
import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
// 🚀 【730task集成】新架构组件导入
// 🔥 【架构简化】移除ThinkingBoxAdapter依赖，直接使用DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.internal.adapter.ThinkingBoxStreamAdapter
import com.example.gymbro.features.thinkingbox.internal.provider.ThinkingBoxViewModelProvider
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import timber.log.Timber
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * ThinkingBoxModule - Hilt 依赖注入模块
 *
 * 提供ThinkingBox模块所需的基础依赖
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class ThinkingBoxModule {

    // 🧹 ARCHITECTURE FIX: HistoryRepository绑定已移至Data层的DI模块
    // 遵循Clean Architecture原则，Features层不应直接绑定Data层实现

    // 🧹 DEPRECATED CLEANUP: ThinkingBoxDisplay接口已废弃并删除
    // 所有功能已迁移到ThinkingBoxLauncher，提供统一的API和错误处理

    /**
     * 🔥 【V2系统提升】DomainMapperFactory已移除，V2系统不需要工厂模式
     */

    companion object {
        // 注意：StreamingThinkingMLParser, ThinkingPhaseExtractor, EventConverter,
        // OptimizedEventConverter, ThinkingMLGuardrail, ProgressiveRenderer 都已经有 @Inject 构造函数，
        // 不需要 @Provides 方法，Hilt 会自动处理依赖注入

        /**
         * 提供 ThinkingBox 专用的 CoroutineScope
         */
        @Provides
        @Singleton
        @ThinkingBoxScope
        fun provideThinkingBoxCoroutineScope(): CoroutineScope = CoroutineScope(
            SupervisorJob() + Dispatchers.Default,
        )

        // 🔥 【日志统一】移除重复的RawThinkingLogger，使用ThinkingBoxLogTree统一管理

        // TokenizerService 现在由 CoreMlBindsModule 提供 OpenAiTokenizer 实现
        // 移除重复绑定以避免 Hilt DuplicateBindings 错误

        // 🔥 【调试工具】TokenFlowFixValidation 使用 @Inject 构造函数，无需手动提供

        /**
         * 🔥 【API统一重构】提供ThinkingBoxLauncherImpl依赖
         *
         * 重构后依赖注入说明：
         * - DirectOutputChannel: 来自CoreNetworkModule，唯一数据源
         * - StreamingThinkingMLParser: 通过@Inject构造函数自动注入，负责语义解析
         * - DomainMapper: 通过@Inject构造函数自动注入，负责事件映射
         * - IoDispatcher: 用于协程调度
         *
         * 🔥 【架构修复】：
         * - 移除对ThinkingBoxStreamAdapter的依赖
         * - 直接注入DirectOutputChannel进行token流处理
         * - 确保ThinkingBoxLauncherImpl能够正确获取所有必要依赖
         */

        // 🔥 【废弃】ThinkingBoxStreamAdapter已被移除，不再需要
        // ThinkingBoxLauncherImpl现在直接订阅DirectOutputChannel

        /**
         * 🔥 【API统一】确保ThinkingBoxLauncherImpl的依赖正确注入
         *
         * 注意：ThinkingBoxLauncherImpl使用@Inject构造函数，
         * 所以Hilt会自动处理依赖注入，这里不需要手动提供。
         * 但我们需要确保所有依赖都可用：
         * - DirectOutputChannel ✅ (来自CoreNetworkModule)
         * - StreamingThinkingMLParser ✅ (通过@Inject构造函数)
         * - DomainMapper ✅ (通过@Inject构造函数)
         * - IoDispatcher ✅ (来自CoreModule)
         */
    }
}

/**
 * ThinkingBox 模块专用的 CoroutineScope 限定符
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ThinkingBoxScope
