package com.example.gymbro.features.auth.ui.login

import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * Auth登录屏幕常量配置 - 迁移到新设计系统
 * 集中管理所有设计Token引用，消除硬编码魔数
 */
object AuthLoginConstants {
    // === 尺寸常量 - 使用Token系统 ===

    /** Logo区域大小 */
    val LOGO_SIZE = Tokens.Spacing.Massive * 5 // 320dp equivalent

    /** 按钮高度 */
    val BUTTON_HEIGHT = Tokens.Spacing.Massive // 64dp

    /** 按钮间距 */
    val BUTTON_SPACING = Tokens.Spacing.Medium // 16dp

    /** 水平内边距 */
    val HORIZONTAL_PADDING = Tokens.Spacing.Large // 24dp

    /** 顶部内边距 */
    val TOP_PADDING = Tokens.Spacing.XLarge + Tokens.Spacing.Small // 40dp

    /** Logo下方间距 */
    val LOGO_BOTTOM_SPACING = Tokens.Spacing.XXLarge + Tokens.Spacing.XLarge // 80dp

    /** 底部间距 */
    val BOTTOM_SPACING = Tokens.Spacing.XXLarge + Tokens.Spacing.XLarge // 80dp

    /** Logo标题间距 */
    val LOGO_TITLE_SPACING = Tokens.Spacing.Small // 8dp

    /** 标题副标题间距 */
    val TITLE_SUBTITLE_SPACING = Tokens.Spacing.Medium // 16dp

    // === 文字大小 ===

    /** 应用名称字体大小 */
    val APP_NAME_FONT_SIZE = 52.sp

    /** 应用名称行高 */
    val APP_NAME_LINE_HEIGHT = 56.sp

    /** 副标题字体大小 */
    val SUBTITLE_FONT_SIZE = 16.sp

    /** 打字机效果字体大小 */
    val TYPEWRITER_FONT_SIZE = 16.sp

    // === 透明度常量 ===

    /** 副标题透明度 */
    const val SUBTITLE_ALPHA = 0.7f

    /** 打字机效果透明度 */
    const val TYPEWRITER_ALPHA = 0.7f

    // === 动画常量 - 使用AnimToken系统 ===

    /** Logo进入动画延迟时间 */
    const val LOGO_ENTER_DELAY_MS = 200

    /** 内容进入偏移量 - 🧹 HARDCODE CLEANUP: 使用Token系统 */
    val CONTENT_ENTER_OFFSET = Tokens.Spacing.Massive // 96.dp - 使用设计系统定义的最大间距

    /** 背景图片宽高比 */
    const val BACKGROUND_ASPECT_RATIO = 856f / 456f

    // === 背景图片资源 ===

    /** 深色主题背景图片 */
    val DARK_BACKGROUND_DRAWABLE = com.example.gymbro.designSystem.R.drawable.auth_globe_dark

    /** 浅色主题背景图片 */
    val LIGHT_BACKGROUND_DRAWABLE = com.example.gymbro.designSystem.R.drawable.auth_globe_light

    // === 可访问性描述 ===

    /** 背景图片内容描述 */
    const val BACKGROUND_CONTENT_DESCRIPTION = "健身应用登录背景，展示现代化的地球网格效果"

    /** Logo区域内容描述 */
    const val LOGO_CONTENT_DESCRIPTION = "GymBro应用标志，带有旋转星环装饰效果"

    /** 加载状态内容描述 */
    const val LOADING_CONTENT_DESCRIPTION = "正在处理登录请求，请稍候"
}
