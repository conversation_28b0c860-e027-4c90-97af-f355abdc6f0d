package com.example.gymbro.features.thinkingbox.integration

import app.cash.turbine.test
import com.example.gymbro.core.network.DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxReducer
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ThinkingBox MVI集成测试 - Plan B重构版本
 * 
 * 🔥 【Plan B重构】验证完整的MVI架构流程，确保sessionId移除后的端到端功能
 */
class ThinkingBoxMVIIntegrationTest {

    private lateinit var viewModel: ThinkingBoxViewModel
    private lateinit var mockDirectOutputChannel: DirectOutputChannel

    @BeforeEach
    fun setup() {
        mockDirectOutputChannel = mockk()
        
        // 使用真实的组件进行集成测试
        viewModel = ThinkingBoxViewModel(
            thinkingBoxReducer = ThinkingBoxReducer(),
            domainMapper = DomainMapper(),
            streamingParser = StreamingThinkingMLParser(),
            directOutputChannel = mockDirectOutputChannel
        )
    }

    @Test
    @DisplayName("【端到端】完整MVI流程应该正确执行")
    fun `should execute complete MVI flow correctly`() = runTest {
        // Given - 完整的AI响应token流
        val messageId = "integration-test-123"
        val completeTokenStream = flowOf(
            "<thinking>",
            "我需要分析用户的健身需求...",
            "首先考虑用户的体能水平",
            "</thinking>",
            "<final>",
            "基于您的情况，我推荐以下训练计划：",
            "1. 有氧运动：每周3次，每次30分钟",
            "2. 力量训练：每周2次，全身训练",
            "</final>"
        )
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId) 
        } returns completeTokenStream
        
        // When - 启动完整流程
        viewModel.initialize(messageId)
        
        // Then - 验证MVI模式的完整执行
        viewModel.state.test {
            // 1. 初始状态
            val initialState = awaitItem()
            assertEquals(messageId, initialState.messageId)
            // 🔥 【Plan B重构】不再验证sessionId
            assertTrue(initialState.segmentsQueue.isEmpty())
            assertFalse(initialState.finalReady)
            
            // 等待token流处理完成
            kotlinx.coroutines.delay(500)
            
            // 2. 验证最终状态（可能需要多次awaitItem来获取最新状态）
            val finalState = expectMostRecentItem()
            assertEquals(messageId, finalState.messageId)
            assertTrue(finalState.thinkingClosed)
            assertTrue(finalState.finalReady)
            assertTrue(finalState.finalContent.contains("训练计划"))
        }
    }

    @Test
    @DisplayName("【MVI架构】Intent分发应该正确更新State")
    fun `should dispatch intents and update state correctly`() = runTest {
        // Given
        val messageId = "mvi-test-456"
        val tokenStream = flowOf("<thinking>", "思考内容", "</thinking>")
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId) 
        } returns tokenStream
        
        // When & Then - 测试Initialize Intent
        viewModel.state.test {
            val initialState = awaitItem()
            assertEquals("", initialState.messageId)
            
            // 分发Initialize Intent
            viewModel.initialize(messageId)
            
            val updatedState = awaitItem()
            assertEquals(messageId, updatedState.messageId)
            // 🔥 【Plan B重构】验证messageId设置正确，不再验证sessionId
        }
    }

    @Test
    @DisplayName("【MVI架构】Reset Intent应该正确重置状态")
    fun `should reset state correctly with Reset intent`() = runTest {
        // Given - 先设置一些状态
        val messageId = "reset-test-789"
        val tokenStream = flowOf("<thinking>", "内容", "</thinking>")
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId) 
        } returns tokenStream
        
        viewModel.initialize(messageId)
        
        // When - 重置状态
        viewModel.reset()
        
        // Then
        viewModel.state.test {
            val resetState = awaitItem()
            assertEquals("", resetState.messageId)
            assertTrue(resetState.segmentsQueue.isEmpty())
            assertFalse(resetState.finalReady)
            assertEquals("", resetState.finalContent)
            assertFalse(resetState.thinkingClosed)
        }
    }

    @Test
    @DisplayName("【MVI架构】UiSegmentRendered Intent应该正确更新渲染状态")
    fun `should update segment rendered state correctly`() = runTest {
        // Given - 先创建一个有segments的状态
        val messageId = "segment-test-101"
        val tokenStream = flowOf(
            "<thinking>",
            "思考内容",
            "</thinking>"
        )
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId) 
        } returns tokenStream
        
        viewModel.initialize(messageId)
        
        // 等待segments创建
        kotlinx.coroutines.delay(200)
        
        // When - 标记segment为已渲染
        val segmentId = "thinking-segment-1" // 假设的segment ID
        viewModel.onSegmentRendered(segmentId)
        
        // Then
        viewModel.state.test {
            val state = awaitItem()
            // 验证segment的渲染状态更新
            val renderedSegment = state.segmentsQueue.find { it.id == segmentId }
            if (renderedSegment != null) {
                assertTrue(renderedSegment.isRendered)
            }
        }
    }

    @Test
    @DisplayName("【Effect处理】Effects应该正确发射")
    fun `should emit effects correctly`() = runTest {
        // Given
        val messageId = "effect-test-202"
        val tokenStream = flowOf("<thinking>", "内容", "</thinking>")
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId) 
        } returns tokenStream
        
        // When
        viewModel.initialize(messageId)
        
        // Then - 验证StartTokenStreamListening Effect
        viewModel.effects.test {
            val effect = awaitItem()
            assertTrue(effect is ThinkingBoxContract.Effect.StartTokenStreamListening)
            val streamEffect = effect as ThinkingBoxContract.Effect.StartTokenStreamListening
            assertEquals(messageId, streamEffect.messageId)
        }
    }

    @Test
    @DisplayName("【错误处理】ClearError Intent应该正确清除错误")
    fun `should clear error correctly with ClearError intent`() = runTest {
        // Given - 先设置错误状态（通过某种方式触发错误）
        // 这里我们直接测试clearError方法
        
        // When
        viewModel.clearError()
        
        // Then
        viewModel.state.test {
            val state = awaitItem()
            assertEquals(null, state.error)
        }
    }

    @Test
    @DisplayName("【Token流处理】复杂token流应该正确解析")
    fun `should parse complex token stream correctly`() = runTest {
        // Given - 复杂的token流
        val messageId = "complex-test-303"
        val complexTokenStream = flowOf(
            "<thinking>",
            "开始分析健身需求",
            "<phase id='assessment'>",
            "评估用户体能水平",
            "- 年龄：25岁",
            "- 运动经验：初学者",
            "</phase>",
            "<phase id='planning'>",
            "制定训练计划",
            "- 目标：增肌",
            "- 频率：每周4次",
            "</phase>",
            "分析完成",
            "</thinking>",
            "<final>",
            "## 个性化训练方案",
            "",
            "基于您的情况，推荐：",
            "1. **有氧训练**：每周3次",
            "2. **力量训练**：每周3次",
            "3. **休息日**：每周1天",
            "</final>"
        )
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId) 
        } returns complexTokenStream
        
        // When
        viewModel.initialize(messageId)
        
        // 等待处理完成
        kotlinx.coroutines.delay(1000)
        
        // Then
        viewModel.state.test {
            val finalState = expectMostRecentItem()
            
            // 验证基本状态
            assertEquals(messageId, finalState.messageId)
            assertTrue(finalState.thinkingClosed)
            assertTrue(finalState.finalReady)
            
            // 验证segments创建
            assertTrue(finalState.segmentsQueue.isNotEmpty())
            
            // 验证final content
            assertTrue(finalState.finalContent.contains("个性化训练方案"))
            assertTrue(finalState.finalContent.contains("有氧训练"))
            assertTrue(finalState.finalContent.contains("力量训练"))
        }
    }

    @Test
    @DisplayName("【并发处理】多个messageId应该正确隔离")
    fun `should handle multiple messageIds correctly`() = runTest {
        // Given
        val messageId1 = "concurrent-test-1"
        val messageId2 = "concurrent-test-2"
        
        val tokenStream1 = flowOf("<thinking>", "第一个消息", "</thinking>")
        val tokenStream2 = flowOf("<thinking>", "第二个消息", "</thinking>")
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId1) 
        } returns tokenStream1
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId2) 
        } returns tokenStream2
        
        // When - 先初始化第一个消息
        viewModel.initialize(messageId1)
        kotlinx.coroutines.delay(100)
        
        // 然后初始化第二个消息（应该覆盖第一个）
        viewModel.initialize(messageId2)
        kotlinx.coroutines.delay(100)
        
        // Then
        viewModel.state.test {
            val finalState = expectMostRecentItem()
            assertEquals(messageId2, finalState.messageId)
            // 应该只处理最新的messageId
        }
    }
}
