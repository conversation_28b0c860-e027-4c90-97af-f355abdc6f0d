package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.model.SemanticEvent
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * StreamingThinkingMLParser测试 - Plan B重构版本
 * 
 * 🔥 【Plan B重构】验证Token解析逻辑，确保XML解析功能完整性
 */
class StreamingThinkingMLParserTest {

    private lateinit var parser: StreamingThinkingMLParser

    @BeforeEach
    fun setup() {
        parser = StreamingThinkingMLParser()
    }

    @Test
    @DisplayName("【基础解析】thinking标签解析应该正确")
    fun `should parse thinking tags correctly`() {
        // Given
        val messageId = "test-message-123"
        val events = mutableListOf<SemanticEvent>()
        
        // When
        parser.parseTokenChunk("<thinking>", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("这是思考内容", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("</thinking>", messageId) { event ->
            events.add(event)
        }
        
        // Then
        assertEquals(3, events.size)
        
        // 验证ThinkingStart事件
        assertTrue(events[0] is SemanticEvent.ThinkingStart)
        val startEvent = events[0] as SemanticEvent.ThinkingStart
        assertEquals(messageId, startEvent.messageId)
        
        // 验证ThinkingContent事件
        assertTrue(events[1] is SemanticEvent.ThinkingContent)
        val contentEvent = events[1] as SemanticEvent.ThinkingContent
        assertEquals(messageId, contentEvent.messageId)
        assertEquals("这是思考内容", contentEvent.content)
        
        // 验证ThinkingEnd事件
        assertTrue(events[2] is SemanticEvent.ThinkingEnd)
        val endEvent = events[2] as SemanticEvent.ThinkingEnd
        assertEquals(messageId, endEvent.messageId)
    }

    @Test
    @DisplayName("【基础解析】phase标签解析应该正确")
    fun `should parse phase tags correctly`() {
        // Given
        val messageId = "test-message-456"
        val events = mutableListOf<SemanticEvent>()
        
        // When
        parser.parseTokenChunk("<phase id='analysis'>", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("分析阶段内容", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("</phase>", messageId) { event ->
            events.add(event)
        }
        
        // Then
        assertEquals(3, events.size)
        
        // 验证PhaseStart事件
        assertTrue(events[0] is SemanticEvent.PhaseStart)
        val startEvent = events[0] as SemanticEvent.PhaseStart
        assertEquals(messageId, startEvent.messageId)
        assertEquals("analysis", startEvent.phaseId)
        
        // 验证PhaseContent事件
        assertTrue(events[1] is SemanticEvent.PhaseContent)
        val contentEvent = events[1] as SemanticEvent.PhaseContent
        assertEquals(messageId, contentEvent.messageId)
        assertEquals("analysis", contentEvent.phaseId)
        assertEquals("分析阶段内容", contentEvent.content)
        
        // 验证PhaseEnd事件
        assertTrue(events[2] is SemanticEvent.PhaseEnd)
        val endEvent = events[2] as SemanticEvent.PhaseEnd
        assertEquals(messageId, endEvent.messageId)
        assertEquals("analysis", endEvent.phaseId)
    }

    @Test
    @DisplayName("【基础解析】final标签解析应该正确")
    fun `should parse final tags correctly`() {
        // Given
        val messageId = "test-message-789"
        val events = mutableListOf<SemanticEvent>()
        
        // When
        parser.parseTokenChunk("<final>", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("最终答案内容", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("</final>", messageId) { event ->
            events.add(event)
        }
        
        // Then
        assertEquals(3, events.size)
        
        // 验证FinalStart事件
        assertTrue(events[0] is SemanticEvent.FinalStart)
        val startEvent = events[0] as SemanticEvent.FinalStart
        assertEquals(messageId, startEvent.messageId)
        
        // 验证FinalContent事件
        assertTrue(events[1] is SemanticEvent.FinalContent)
        val contentEvent = events[1] as SemanticEvent.FinalContent
        assertEquals(messageId, contentEvent.messageId)
        assertEquals("最终答案内容", contentEvent.content)
        
        // 验证FinalEnd事件
        assertTrue(events[2] is SemanticEvent.FinalEnd)
        val endEvent = events[2] as SemanticEvent.FinalEnd
        assertEquals(messageId, endEvent.messageId)
    }

    @Test
    @DisplayName("【边界条件】空token应该正确处理")
    fun `should handle empty token correctly`() {
        // Given
        val messageId = "test-empty"
        val events = mutableListOf<SemanticEvent>()
        
        // When
        parser.parseTokenChunk("", messageId) { event ->
            events.add(event)
        }
        
        // Then
        assertTrue(events.isEmpty())
    }

    @Test
    @DisplayName("【边界条件】纯文本token应该正确处理")
    fun `should handle plain text token correctly`() {
        // Given
        val messageId = "test-plain"
        val events = mutableListOf<SemanticEvent>()
        
        // When
        parser.parseTokenChunk("这是普通文本", messageId) { event ->
            events.add(event)
        }
        
        // Then
        // 普通文本在没有上下文的情况下可能不产生事件，或产生PlainText事件
        // 具体行为取决于实现
    }

    @Test
    @DisplayName("【复杂场景】嵌套标签应该正确解析")
    fun `should parse nested tags correctly`() {
        // Given
        val messageId = "test-nested"
        val events = mutableListOf<SemanticEvent>()
        
        // When - 模拟完整的AI响应
        val tokens = listOf(
            "<thinking>",
            "开始分析问题",
            "<phase id='step1'>",
            "第一步分析",
            "</phase>",
            "<phase id='step2'>",
            "第二步分析",
            "</phase>",
            "分析完成",
            "</thinking>",
            "<final>",
            "基于分析的最终答案",
            "</final>"
        )
        
        tokens.forEach { token ->
            parser.parseTokenChunk(token, messageId) { event ->
                events.add(event)
            }
        }
        
        // Then
        assertTrue(events.size >= 10) // 至少应该有多个事件
        
        // 验证事件序列的正确性
        assertTrue(events[0] is SemanticEvent.ThinkingStart)
        assertTrue(events.any { it is SemanticEvent.PhaseStart })
        assertTrue(events.any { it is SemanticEvent.PhaseEnd })
        assertTrue(events.any { it is SemanticEvent.ThinkingEnd })
        assertTrue(events.any { it is SemanticEvent.FinalStart })
        assertTrue(events.any { it is SemanticEvent.FinalEnd })
    }

    @Test
    @DisplayName("【错误处理】不匹配的标签应该正确处理")
    fun `should handle mismatched tags correctly`() {
        // Given
        val messageId = "test-mismatch"
        val events = mutableListOf<SemanticEvent>()
        
        // When - 不匹配的标签
        parser.parseTokenChunk("<thinking>", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("内容", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("</phase>", messageId) { event ->
            events.add(event)
        }
        
        // Then
        // 应该至少有ThinkingStart和Content事件
        assertTrue(events.size >= 2)
        assertTrue(events[0] is SemanticEvent.ThinkingStart)
        // 错误的结束标签可能被忽略或产生错误事件
    }

    @Test
    @DisplayName("【性能测试】大量token应该正确处理")
    fun `should handle large number of tokens correctly`() {
        // Given
        val messageId = "test-performance"
        val events = mutableListOf<SemanticEvent>()
        
        // When - 大量小token
        parser.parseTokenChunk("<thinking>", messageId) { event ->
            events.add(event)
        }
        
        repeat(1000) { i ->
            parser.parseTokenChunk("内容$i ", messageId) { event ->
                events.add(event)
            }
        }
        
        parser.parseTokenChunk("</thinking>", messageId) { event ->
            events.add(event)
        }
        
        // Then
        assertTrue(events.size >= 2) // 至少有开始和结束事件
        assertTrue(events[0] is SemanticEvent.ThinkingStart)
        assertTrue(events.last() is SemanticEvent.ThinkingEnd)
    }

    @Test
    @DisplayName("【状态管理】解析器状态应该正确维护")
    fun `should maintain parser state correctly`() {
        // Given
        val messageId = "test-state"
        val events = mutableListOf<SemanticEvent>()
        
        // When - 分步解析标签
        parser.parseTokenChunk("<think", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("ing>", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("内容", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("</think", messageId) { event ->
            events.add(event)
        }
        parser.parseTokenChunk("ing>", messageId) { event ->
            events.add(event)
        }
        
        // Then
        // 应该正确识别完整的标签
        assertTrue(events.any { it is SemanticEvent.ThinkingStart })
        assertTrue(events.any { it is SemanticEvent.ThinkingContent })
        assertTrue(events.any { it is SemanticEvent.ThinkingEnd })
    }
}
