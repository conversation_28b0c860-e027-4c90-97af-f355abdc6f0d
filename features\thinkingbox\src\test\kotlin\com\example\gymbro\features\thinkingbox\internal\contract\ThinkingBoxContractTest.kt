package com.example.gymbro.features.thinkingbox.internal.contract

import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ThinkingBoxContract测试 - Plan B重构版本
 * 
 * 🔥 【Plan B重构】验证Contract结构正确性，确保sessionId移除后的功能完整性
 */
class ThinkingBoxContractTest {

    @Test
    @DisplayName("【Contract结构】State初始化应该正确")
    fun `should initialize state correctly`() {
        // Given & When
        val state = ThinkingBoxContract.State()
        
        // Then
        assertEquals("", state.messageId)
        assertTrue(state.segmentsQueue.isEmpty())
        assertFalse(state.finalReady)
        assertEquals("", state.finalContent)
        assertFalse(state.thinkingClosed)
        assertEquals(null, state.error)
        assertFalse(state.isLoading)
    }

    @Test
    @DisplayName("【Contract结构】State with messageId应该正确设置")
    fun `should set messageId correctly`() {
        // Given
        val messageId = "test-message-123"
        
        // When
        val state = ThinkingBoxContract.State(messageId = messageId)
        
        // Then
        assertEquals(messageId, state.messageId)
        // 🔥 【Plan B重构】不再验证sessionId，通过ConversationIdManager获取
    }

    @Test
    @DisplayName("【Contract结构】State with segments应该正确设置")
    fun `should set segments queue correctly`() {
        // Given
        val segments = listOf(
            ThinkingBoxContract.SegmentUi(
                id = "segment-1",
                kind = SegmentKind.THINKING,
                title = "思考阶段",
                content = "正在分析...",
                isComplete = true,
                isRendered = false
            )
        )
        
        // When
        val state = ThinkingBoxContract.State(segmentsQueue = segments)
        
        // Then
        assertEquals(1, state.segmentsQueue.size)
        assertEquals("segment-1", state.segmentsQueue[0].id)
        assertEquals(SegmentKind.THINKING, state.segmentsQueue[0].kind)
        assertEquals("思考阶段", state.segmentsQueue[0].title)
        assertEquals("正在分析...", state.segmentsQueue[0].content)
        assertTrue(state.segmentsQueue[0].isComplete)
        assertFalse(state.segmentsQueue[0].isRendered)
    }

    @Test
    @DisplayName("【Contract结构】State with final content应该正确设置")
    fun `should set final content correctly`() {
        // Given
        val finalContent = "这是最终答案"
        
        // When
        val state = ThinkingBoxContract.State(
            finalReady = true,
            finalContent = finalContent,
            thinkingClosed = true
        )
        
        // Then
        assertTrue(state.finalReady)
        assertEquals(finalContent, state.finalContent)
        assertTrue(state.thinkingClosed)
    }

    @Test
    @DisplayName("【四条铁律】shouldShowAIThinkingCard应该正确计算")
    fun `should calculate shouldShowAIThinkingCard correctly`() {
        // Given - 有segments的情况
        val stateWithSegments = ThinkingBoxContract.State(
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-1",
                    kind = SegmentKind.THINKING,
                    title = "思考",
                    content = "内容",
                    isComplete = true,
                    isRendered = false
                )
            )
        )
        
        // When & Then
        assertTrue(stateWithSegments.shouldShowAIThinkingCard)
        
        // Given - 空segments但思考未关闭
        val stateStreaming = ThinkingBoxContract.State(
            segmentsQueue = emptyList(),
            thinkingClosed = false
        )
        
        // When & Then - 需要检查isStreaming()方法
        // 注意：这里需要根据实际的isStreaming()实现来验证
        // assertTrue(stateStreaming.shouldShowAIThinkingCard)
        
        // Given - 空segments且思考已关闭
        val stateEmpty = ThinkingBoxContract.State(
            segmentsQueue = emptyList(),
            thinkingClosed = true
        )
        
        // When & Then
        assertFalse(stateEmpty.shouldShowAIThinkingCard)
    }

    @Test
    @DisplayName("【四条铁律】shouldShowFinalContent应该正确计算")
    fun `should calculate shouldShowFinalContent correctly`() {
        // Given - final ready且有内容
        val stateReady = ThinkingBoxContract.State(
            finalReady = true,
            finalContent = "最终答案内容"
        )
        
        // When & Then
        assertTrue(stateReady.shouldShowFinalContent)
        
        // Given - final ready但无内容
        val stateNoContent = ThinkingBoxContract.State(
            finalReady = true,
            finalContent = ""
        )
        
        // When & Then
        assertFalse(stateNoContent.shouldShowFinalContent)
        
        // Given - 有内容但final未ready
        val stateNotReady = ThinkingBoxContract.State(
            finalReady = false,
            finalContent = "最终答案内容"
        )
        
        // When & Then
        assertFalse(stateNotReady.shouldShowFinalContent)
    }

    @Test
    @DisplayName("【Intent结构】Initialize Intent应该正确创建")
    fun `should create Initialize intent correctly`() {
        // Given
        val messageId = "test-message-456"
        
        // When
        val intent = ThinkingBoxContract.Intent.Initialize(messageId)
        
        // Then
        assertEquals(messageId, intent.messageId)
        // 🔥 【Plan B重构】不再包含sessionId参数
    }

    @Test
    @DisplayName("【Intent结构】UiSegmentRendered Intent应该正确创建")
    fun `should create UiSegmentRendered intent correctly`() {
        // Given
        val segmentId = "segment-123"
        
        // When
        val intent = ThinkingBoxContract.Intent.UiSegmentRendered(segmentId)
        
        // Then
        assertEquals(segmentId, intent.segmentId)
    }

    @Test
    @DisplayName("【Intent结构】Reset和ClearError Intent应该正确创建")
    fun `should create Reset and ClearError intents correctly`() {
        // When
        val resetIntent = ThinkingBoxContract.Intent.Reset
        val clearErrorIntent = ThinkingBoxContract.Intent.ClearError
        
        // Then
        assertEquals(ThinkingBoxContract.Intent.Reset, resetIntent)
        assertEquals(ThinkingBoxContract.Intent.ClearError, clearErrorIntent)
    }

    @Test
    @DisplayName("【Effect结构】StartTokenStreamListening Effect应该正确创建")
    fun `should create StartTokenStreamListening effect correctly`() {
        // Given
        val messageId = "test-message-789"
        
        // When
        val effect = ThinkingBoxContract.Effect.StartTokenStreamListening(messageId)
        
        // Then
        assertEquals(messageId, effect.messageId)
    }

    @Test
    @DisplayName("【Effect结构】NotifyHistory Effects应该正确创建")
    fun `should create NotifyHistory effects correctly`() {
        // Given
        val messageId = "test-message-history"
        val thinkingContent = "思考内容"
        val finalContent = "最终内容"
        
        // When
        val thinkingEffect = ThinkingBoxContract.Effect.NotifyHistoryThinking(
            messageId = messageId,
            thinkingMarkdown = thinkingContent,
            debounceMs = 200L
        )
        val finalEffect = ThinkingBoxContract.Effect.NotifyHistoryFinal(
            messageId = messageId,
            finalMarkdown = finalContent
        )
        
        // Then
        assertEquals(messageId, thinkingEffect.messageId)
        assertEquals(thinkingContent, thinkingEffect.thinkingMarkdown)
        assertEquals(200L, thinkingEffect.debounceMs)
        
        assertEquals(messageId, finalEffect.messageId)
        assertEquals(finalContent, finalEffect.finalMarkdown)
        // 🔥 【Plan B重构】History Effects使用ConversationIdManager获取sessionId
    }

    @Test
    @DisplayName("【Effect结构】UI Effects应该正确创建")
    fun `should create UI effects correctly`() {
        // When
        val scrollEffect = ThinkingBoxContract.Effect.ScrollToBottom
        val closeEffect = ThinkingBoxContract.Effect.CloseThinkingBox
        val debugEffect = ThinkingBoxContract.Effect.LogDebug("测试日志")
        
        // Then
        assertEquals(ThinkingBoxContract.Effect.ScrollToBottom, scrollEffect)
        assertEquals(ThinkingBoxContract.Effect.CloseThinkingBox, closeEffect)
        assertEquals("测试日志", debugEffect.message)
    }

    @Test
    @DisplayName("【SegmentUi结构】SegmentUi应该正确创建")
    fun `should create SegmentUi correctly`() {
        // Given
        val segmentUi = ThinkingBoxContract.SegmentUi(
            id = "test-segment",
            kind = SegmentKind.PHASE,
            title = "测试阶段",
            content = "测试内容",
            isComplete = true,
            isRendered = true
        )
        
        // Then
        assertEquals("test-segment", segmentUi.id)
        assertEquals(SegmentKind.PHASE, segmentUi.kind)
        assertEquals("测试阶段", segmentUi.title)
        assertEquals("测试内容", segmentUi.content)
        assertTrue(segmentUi.isComplete)
        assertTrue(segmentUi.isRendered)
    }
}
