package com.example.gymbro.features.thinkingbox.api

import com.example.gymbro.core.error.types.ModernResult

/**
 * ThinkingBoxLauncher - ThinkingBox显示控制接口
 *
 * 🎯 【Plan B重构】职责分离设计原则：
 * - 专注于ThinkingBox的显示控制，不涉及AI请求发送
 * - 遵循"ThinkingBox只负责接收token流"的架构要求
 * - 统一使用messageId，通过ConversationIdManager获取sessionId
 * - 消除与Coach模块的职责重叠
 *
 * 🔥 核心功能：
 * - 启动和停止ThinkingBox显示
 * - 管理token流订阅和UI渲染
 * - 提供显示状态查询能力
 * - 支持优雅的资源清理机制
 *
 * 架构原则：
 * - 被动接收：只订阅token流，不主动发送AI请求
 * - 统一ID：使用messageId作为唯一标识
 * - 资源管理：proper资源清理和生命周期管理
 * - 状态透明：提供清晰的显示状态信息
 *
 * @since Plan B重构v1.0
 */
interface ThinkingBoxLauncher {

    /**
     * 🔥 【Plan B重构】启动ThinkingBox显示
     *
     * 启动AI思考过程显示，专注于显示控制而非AI处理启动。
     *
     * @param messageId 消息唯一标识符，用于token流订阅
     * @param completionListener 完成回调监听器，用于接收思考完成通知
     * @return 启动操作结果，成功时表示显示已启动，失败时包含错误信息
     *
     * 行为说明：
     * - 初始化ThinkingBox UI组件
     * - 订阅DirectOutputChannel的token流
     * - 开始实时显示AI思考过程
     * - 注册完成回调监听器
     *
     * 实现细节：
     * - 如果messageId已存在，则停止之前的会话并启动新会话
     * - 专注于显示控制，不涉及AI请求发起
     * - 通过ConversationIdManager获取sessionId（如需要）
     *
     * 错误处理：
     * - 如果启动失败，通过ModernResult.Error同步返回
     * - 网络异常或解析错误将通过ModernResult.Error返回
     */
    suspend fun startDisplaying(
        messageId: String,
        completionListener: ThinkingBoxCompletionListener,
    ): ModernResult<Unit>

    /**
     * 🔥 【Plan B重构】停止ThinkingBox显示
     *
     * 优雅地停止正在进行的思考显示。
     *
     * @param messageId 要停止的消息标识符
     * @return 停止操作结果
     *
     * 行为说明：
     * - 停止token流监听和解析
     * - 清理相关UI状态和资源
     * - 取消未完成的渲染任务
     * - 不会触发completionListener回调（主动停止）
     *
     * 实现细节：
     * - 如果messageId不存在或已完成，返回成功结果
     * - 停止操作是幂等的，多次调用不会产生副作用
     * - 确保所有相关协程和资源得到正确清理
     *
     * 应用场景：
     * - 用户主动取消思考过程
     * - 页面导航时清理资源
     * - 系统内存压力下的资源释放
     */
    suspend fun stopDisplaying(messageId: String): ModernResult<Unit>

    /**
     * 🔥 【Plan B重构】检查显示状态
     *
     * 检查指定消息是否正在显示。
     *
     * @param messageId 消息标识符
     * @return 显示状态结果，成功时包含布尔值，失败时包含错误信息
     *
     * 用途：
     * - 避免重复启动同一消息的思考显示
     * - UI状态检查和调试
     * - 资源管理和清理判断
     */
    suspend fun isDisplaying(messageId: String): ModernResult<Boolean>

    /**
     * 🔥 【Plan B重构】获取显示状态
     *
     * 查询指定消息的当前显示状态。
     *
     * @param messageId 消息标识符
     * @return 显示状态信息
     *
     * 状态类型：
     * - Idle: 未开始显示或已完成显示
     * - Displaying: 正在显示中
     * - Completed: 显示完成
     * - Failed: 显示失败
     *
     * 用途：
     * - UI状态同步和显示
     * - 调试和诊断
     * - 重复启动防护
     */
    suspend fun getDisplayStatus(messageId: String): ModernResult<ThinkingBoxDisplayStatus>

    /**
     * 🔥 【Plan B重构】获取活跃显示列表
     *
     * 获取当前正在显示的消息列表。
     *
     * @return 当前活跃的消息ID列表
     *
     * 用途：
     * - 资源监控和管理
     * - 调试和诊断
     * - 批量清理操作
     */
    suspend fun getActiveDisplays(): ModernResult<List<String>>

    /**
     * 🔥 【Plan B重构】清理所有显示会话
     *
     * 清理所有活跃的显示会话。
     *
     * @return 清理操作结果
     *
     * 行为说明：
     * - 停止所有正在进行的思考显示
     * - 清理所有相关资源
     * - 不触发完成回调
     *
     * 应用场景：
     * - 应用退出时的资源清理
     * - 内存压力下的资源释放
     * - 全局重置操作
     */
    suspend fun clearAllDisplays(): ModernResult<Unit>
}

/**
 * ThinkingBoxDisplayStatus - ThinkingBox显示状态
 *
 * 描述ThinkingBox显示过程的当前状态。
 */
sealed class ThinkingBoxDisplayStatus {
    /**
     * 空闲状态：未开始显示或已完成显示
     */
    object Idle : ThinkingBoxDisplayStatus()

    /**
     * 显示中状态：正在进行思考过程显示
     *
     * @param progress 可选的显示进度信息
     */
    data class Displaying(
        val progress: ThinkingProgress? = null,
    ) : ThinkingBoxDisplayStatus()

    /**
     * 完成状态：显示过程已成功完成
     *
     * @param completedAt 完成时间戳（毫秒）
     * @param duration 显示总时长（毫秒）
     */
    data class Completed(
        val completedAt: Long,
        val duration: Long,
    ) : ThinkingBoxDisplayStatus()

    /**
     * 失败状态：显示过程失败或中断
     *
     * @param error 错误信息
     * @param failedAt 失败时间戳（毫秒）
     * @param hasPartialResult 是否有部分显示结果可用
     */
    data class Failed(
        val error: ThinkingBoxDisplayError,
        val failedAt: Long,
        val hasPartialResult: Boolean = false,
    ) : ThinkingBoxDisplayStatus()
}

/**
 * ThinkingBoxDisplayError - ThinkingBox显示错误类型
 *
 * 定义显示过程中各种可能的错误情况和原因。
 */
sealed class ThinkingBoxDisplayError(val message: String, val code: String) {

    /**
     * Token流订阅失败
     */
    data class TokenStreamFailed(
        val reason: String,
        val messageId: String,
    ) : ThinkingBoxDisplayError("Token流订阅失败: $reason", "TOKEN_STREAM_FAILED")

    /**
     * 网络连接错误
     */
    data class NetworkError(
        val reason: String,
        val isRetryable: Boolean = true,
    ) : ThinkingBoxDisplayError("网络错误: $reason", "NETWORK_ERROR")

    /**
     * 显示超时错误
     */
    data class DisplayTimeout(
        val timeoutMs: Long,
        val stage: String = "unknown",
    ) : ThinkingBoxDisplayError("显示超时: ${timeoutMs}ms (阶段: $stage)", "DISPLAY_TIMEOUT")

    /**
     * 无效消息ID错误
     */
    data class InvalidMessageId(
        val messageId: String,
        val reason: String,
    ) : ThinkingBoxDisplayError("无效消息ID: $messageId - $reason", "INVALID_MESSAGE_ID")

    /**
     * 解析错误
     */
    data class ParseError(
        val reason: String,
        val position: Int? = null,
    ) : ThinkingBoxDisplayError("解析错误: $reason", "PARSE_ERROR")

    /**
     * 系统资源不足
     */
    data class ResourceExhausted(
        val resource: String,
        val currentUsage: String? = null,
    ) : ThinkingBoxDisplayError("资源不足: $resource", "RESOURCE_EXHAUSTED")

    /**
     * 未知显示错误
     */
    data class UnknownError(
        val reason: String,
        val originalException: String? = null,
    ) : ThinkingBoxDisplayError("未知显示错误: $reason", "UNKNOWN_ERROR")
}
