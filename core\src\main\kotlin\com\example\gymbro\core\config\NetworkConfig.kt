package com.example.gymbro.core.config

/**
 * 网络配置统一管理
 * 
 * 🧹 HARDCODE CLEANUP: 统一管理所有网络相关的硬编码配置值
 * 
 * 设计原则：
 * - 集中管理所有网络配置
 * - 支持不同环境的配置切换
 * - 提供类型安全的配置访问
 * - 便于测试和调试
 */
object NetworkConfig {
    
    /**
     * API配置
     */
    object Api {
        /**
         * API基础URL
         * 生产环境使用BuildConfig，开发环境可以覆盖
         */
        val BASE_URL: String = BuildConfig.BASE_URL.takeIf { it.isNotBlank() } 
            ?: "https://api.gymbro.app/"
        
        /**
         * API版本
         */
        const val API_VERSION = "v1"
        
        /**
         * 完整的API基础URL
         */
        val FULL_BASE_URL: String = "${BASE_URL}${API_VERSION}/"
    }
    
    /**
     * 超时配置
     */
    object Timeouts {
        /**
         * 标准连接超时时间 (秒)
         */
        const val CONNECT_TIMEOUT = 15L
        
        /**
         * 标准读取超时时间 (秒)
         */
        const val READ_TIMEOUT = 15L
        
        /**
         * 写入超时时间 (秒)
         */
        const val WRITE_TIMEOUT = 15L
        
        /**
         * IP验证专用的快速超时时间 (秒)
         * 优化为3秒，确保在Loading页面2-5秒窗口内完成
         */
        const val IP_CONNECT_TIMEOUT = 3L
        const val IP_READ_TIMEOUT = 3L
        
        /**
         * AI请求超时时间 (毫秒)
         * 用于AI相关的长时间请求
         */
        const val AI_REQUEST_TIMEOUT = 30_000L // 30秒
        
        /**
         * 文件上传超时时间 (秒)
         */
        const val UPLOAD_TIMEOUT = 60L
    }
    
    /**
     * 缓存配置
     */
    object Cache {
        /**
         * HTTP缓存大小 (字节)
         * 默认20MB
         */
        const val HTTP_CACHE_SIZE = 20 * 1024 * 1024L // 20MB
        
        /**
         * 图片缓存大小 (字节)
         * 默认50MB
         */
        const val IMAGE_CACHE_SIZE = 50 * 1024 * 1024L // 50MB
        
        /**
         * 缓存目录名
         */
        const val HTTP_CACHE_DIR = "http_cache"
        const val IMAGE_CACHE_DIR = "image_cache"
    }
    
    /**
     * 重试配置
     */
    object Retry {
        /**
         * 默认重试次数
         */
        const val DEFAULT_RETRY_COUNT = 3
        
        /**
         * 重试延迟 (毫秒)
         */
        const val RETRY_DELAY = 1000L
        
        /**
         * 指数退避因子
         */
        const val BACKOFF_MULTIPLIER = 2.0
    }
    
    /**
     * 限流配置
     */
    object RateLimit {
        /**
         * 每秒最大请求数
         */
        const val MAX_REQUESTS_PER_SECOND = 10
        
        /**
         * 突发请求缓冲区大小
         */
        const val BURST_CAPACITY = 20
    }
    
    /**
     * 调试配置
     */
    object Debug {
        /**
         * 是否启用网络日志
         */
        val ENABLE_LOGGING: Boolean = BuildConfig.DEBUG
        
        /**
         * 是否启用网络拦截器
         */
        val ENABLE_INTERCEPTOR: Boolean = BuildConfig.DEBUG
        
        /**
         * 日志级别
         */
        val LOG_LEVEL: String = if (BuildConfig.DEBUG) "BODY" else "NONE"
    }
}

/**
 * BuildConfig占位符
 * 实际项目中应该从gradle生成的BuildConfig中获取
 */
private object BuildConfig {
    const val DEBUG = true
    const val BASE_URL = "" // 从gradle配置中获取
}
