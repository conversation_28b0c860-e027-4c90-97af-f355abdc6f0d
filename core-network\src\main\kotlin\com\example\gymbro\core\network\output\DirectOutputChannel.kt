package com.example.gymbro.core.network.output

import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.core.network.logging.TokenLogCollector
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.onEach
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 直接输出通道 - 新架构输出层
 *
 * 设计目标：
 * - 直接流式输出到ThinkingBox
 * - 零中间缓冲，最小延迟
 * - 支持多个订阅者
 * - 背压控制和错误恢复
 * - 🔥 【新增】集成RAW TOKEN批量日志采集
 */
@Singleton
class DirectOutputChannel @Inject constructor(
    private val tokenLogCollector: TokenLogCollector, // 🔥 【新增】Token日志采集器
) {

    companion object {
        private val TAG = GymBroLogTags.CoreNetwork.OUTPUT_DIRECT
        private const val OUTPUT_BUFFER_CAPACITY = 64 // 输出缓冲：64个token
        private const val OUTPUT_TOKEN_BATCH_SIZE = 1 // 🔥 【性能优化】减少批量大小，实现即时输出

        // 🏷️ 【日志统计】输出统计配置
        private const val STATS_LOG_INTERVAL = 60_000L // 60秒记录一次统计
    }

    // 直接输出流
    private val _outputFlow = MutableSharedFlow<OutputToken>(
        replay = 0,
        extraBufferCapacity = OUTPUT_BUFFER_CAPACITY,
        onBufferOverflow = kotlinx.coroutines.channels.BufferOverflow.SUSPEND,
    )

    /**
     * 只读的输出流，供ThinkingBox订阅
     */
    val outputFlow: SharedFlow<OutputToken> = _outputFlow.asSharedFlow()

    // 🏷️ 【日志统计】输出统计信息
    @Volatile
    private var totalTokensOutput = 0L
    @Volatile
    private var totalSubscribers = 0L
    @Volatile
    private var activeSubscribers = 0
    @Volatile
    private var lastStatsLogTime = 0L

    // 🔥 【新增】Token收集缓冲区，用于批量日志记录
    private val outputTokenCollectionBuffer = mutableListOf<String>()

    /**
     * 发送处理后的token到输出通道
     * 🔥 【Plan B重构】统一使用messageId，消除conversationId概念
     *
     * @param token 处理后的token内容
     * @param messageId 消息ID（原conversationId）
     * @param contentType 内容类型
     * @param metadata 附加元数据
     */
    suspend fun sendToken(
        token: String,
        messageId: String,
        contentType: com.example.gymbro.core.network.detector.ContentType,
        metadata: Map<String, Any> = emptyMap(),
    ) {
        // 🔥 【Plan B重构】端到端追踪，使用messageId
        Timber.tag(
            "TB-E2E-TRACE",
        ).e(
            "🔍 [输出通道接收] messageId=$messageId, contentType=$contentType, token='${token.take(
                100,
            )}...'",
        )

        if (token.isEmpty()) {
            Timber.tag("TB-E2E-TRACE").e("⚠️ [输出通道跳过] 空token")
            return
        }

        val outputToken = OutputToken(
            content = token,
            messageId = messageId, // 🔥 【Plan B重构】直接使用messageId字段
            contentType = contentType,
            timestamp = System.currentTimeMillis(),
            metadata = metadata,
        )

        try {
            // 🔥 【Plan B重构】数据流验证，使用messageId
            Timber.tag(GymBroLogTags.CoreNetwork.OUTPUT_DIRECT).d("🔍 [数据流] DirectOutputChannel发送: messageId=$messageId, token长度=${token.length}")

            _outputFlow.emit(outputToken)
            totalTokensOutput++

            // 🏷️ 【日志统计】定期记录输出统计
            logOutputStatsIfNeeded()

            // 🔥 【RAW TOKEN日志采集】收集输出token，使用messageId
            collectOutputTokenForLogging(token, "ThinkingBox", messageId)

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ [输出失败] messageId=$messageId, error=${e.message}")
            throw e
        }
    }

    /**
     * 批量发送token（用于缓冲区清空）
     * 🔥 【Plan B重构】使用messageId参数
     *
     * @param tokens token列表
     * @param messageId 消息ID（原conversationId）
     * @param contentType 内容类型
     */
    suspend fun sendTokenBatch(
        tokens: List<String>,
        messageId: String,
        contentType: com.example.gymbro.core.network.detector.ContentType,
    ) {
        if (tokens.isEmpty()) return

        val timestamp = System.currentTimeMillis()

        tokens.forEach { token ->
            if (token.isNotEmpty()) {
                val outputToken = OutputToken(
                    content = token,
                    messageId = messageId, // 🔥 【Plan B重构】直接使用messageId字段
                    contentType = contentType,
                    timestamp = timestamp,
                    metadata = mapOf("batch" to true),
                )

                _outputFlow.emit(outputToken)
                totalTokensOutput++

                // 🔥 【Plan B重构】收集批量输出token，使用messageId
                collectOutputTokenForLogging(token, "ThinkingBox", messageId)
            }
        }

        Timber.tag(TAG).d(
            "📤 批量输出: messageId=$messageId, " +
                "count=${tokens.size}, type=$contentType",
        )
    }

    /**
     * 订阅特定消息的输出流
     * 🔥 【Plan B重构】重命名方法，使用messageId参数
     *
     * @param messageId 消息ID（原conversationId）
     * @return 过滤后的输出流
     */
    fun subscribeToMessage(messageId: String): Flow<OutputToken> {
        activeSubscribers++
        totalSubscribers++

        Timber.tag(TAG).d("🔗 [订阅] messageId=$messageId, 活跃订阅者=$activeSubscribers")

        return outputFlow
            .filter { token ->
                token.messageId == messageId // 🔥 【Plan B重构】直接使用messageId字段
            }
            .onEach { outputToken ->
                // 🔥 【Plan B重构】端到端追踪，使用messageId
                Timber.tag(
                    "TB-E2E-TRACE",
                ).e("📨 [输出通道分发] messageId=$messageId, token='${outputToken.content.take(100)}...'")
            }
    }

    // 🧹 REMOVED: subscribeToConversation废弃方法已删除
    // 使用subscribeToMessage替代，统一使用messageId参数

    /**
     * 取消订阅
     */
    fun unsubscribe() {
        activeSubscribers = maxOf(0, activeSubscribers - 1)
    }

    /**
     * 获取输出通道状态
     */
    fun getChannelStatus(): OutputChannelStatus {
        return OutputChannelStatus(
            totalTokensOutput = totalTokensOutput,
            activeSubscribers = activeSubscribers,
            bufferCapacity = OUTPUT_BUFFER_CAPACITY,
        )
    }

    /**
     * 清理输出通道
     */
    suspend fun cleanup() {
        // 🔥 【RAW TOKEN日志采集】最终刷新输出token缓冲区
        flushOutputTokenCollectionBuffer("ThinkingBox", "cleanup")

        Timber.tag(TAG).i(
            "🧹 清理DirectOutputChannel: totalTokens=$totalTokensOutput, " +
                "subscribers=$activeSubscribers",
        )
        activeSubscribers = 0
    }

    /**
     * 🔥 【RAW TOKEN日志采集】收集输出token用于批量日志记录
     * 🔥 【Plan B重构】使用messageId参数
     */
    private suspend fun collectOutputTokenForLogging(token: String, target: String, messageId: String) {
        // 使用一个局部变量来避免在临界区内调用挂起函数
        val shouldFlush: Boolean
        val tokensToFlush: List<String>

        synchronized(outputTokenCollectionBuffer) {
            outputTokenCollectionBuffer.add(token)

            // 检查是否需要刷新
            shouldFlush = outputTokenCollectionBuffer.size >= OUTPUT_TOKEN_BATCH_SIZE
            tokensToFlush = if (shouldFlush) {
                val tokens = outputTokenCollectionBuffer.toList()
                outputTokenCollectionBuffer.clear()
                tokens
            } else {
                emptyList()
            }
        }

        // 在临界区外执行挂起函数
        if (shouldFlush) {
            try {
                tokenLogCollector.collectOutputTokens(
                    tokens = tokensToFlush,
                    target = target,
                    messageId = messageId, // 🔥 【Plan B重构】使用messageId参数名
                )
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ 输出Token日志采集失败: target=$target")
            }
        }
    }

    /**
     * 🔥 【RAW TOKEN日志采集】刷新输出缓冲区中剩余的token
     * 🔥 【Plan B重构】使用messageId参数
     */
    private suspend fun flushOutputTokenCollectionBuffer(target: String, messageId: String) {
        val tokensToFlush: List<String>

        synchronized(outputTokenCollectionBuffer) {
            tokensToFlush = if (outputTokenCollectionBuffer.isNotEmpty()) {
                val tokens = outputTokenCollectionBuffer.toList()
                outputTokenCollectionBuffer.clear()
                tokens
            } else {
                emptyList()
            }
        }

        // 在临界区外执行挂起函数
        if (tokensToFlush.isNotEmpty()) {
            try {
                tokenLogCollector.collectOutputTokens(
                    tokens = tokensToFlush,
                    target = target,
                    messageId = messageId, // 🔥 【Plan B重构】使用messageId参数名
                )
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ 最终输出Token刷新失败: target=$target")
            }
        }
    }

    /**
     * 🏷️ 【日志统计】定期记录输出统计
     */
    private fun logOutputStatsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastStatsLogTime >= STATS_LOG_INTERVAL) {
            Timber.tag(TAG).i(
                "📊 [输出统计] 已输出tokens=$totalTokensOutput, " +
                "活跃订阅者=$activeSubscribers, 总订阅者=$totalSubscribers"
            )

            lastStatsLogTime = currentTime
        }
    }
}

/**
 * 📤 输出Token数据结构
 * 🔥 【Plan B重构】统一使用messageId，保持向后兼容
 */
data class OutputToken(
    val content: String,
    val messageId: String, // 🔥 【Plan B重构】主要字段改为messageId
    val contentType: com.example.gymbro.core.network.detector.ContentType,
    val timestamp: Long,
    val metadata: Map<String, Any> = emptyMap(),
) {
    /**
     * 🔥 【Plan B重构】向后兼容属性
     * @deprecated 使用messageId代替
     */
    @Deprecated("使用messageId代替", ReplaceWith("messageId"))
    val conversationId: String get() = messageId
}

/**
 * 📊 输出通道状态信息
 */
data class OutputChannelStatus(
    val totalTokensOutput: Long,
    val activeSubscribers: Int,
    val bufferCapacity: Int,
)
