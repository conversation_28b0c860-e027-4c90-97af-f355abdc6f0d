package com.example.gymbro.domain.shared.base

// 🧹 HARDCODE CLEANUP: 使用统一配置管理系统
import com.example.gymbro.core.config.AiConfig
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout

/**
 * 带超时处理的Modern Flow UseCase基类
 *
 * 为UseCase提供统一的超时处理和协程上下文传播机制
 * 这个基类适用于需要异步处理并可能受网络延迟影响的用例
 *
 * @param P 参数类型
 * @param R 结果类型
 * @param timeoutMs 超时毫秒数，默认10秒
 * @param dispatcher 协程调度器，决定执行上下文
 * @param logger 日志记录器
 */
abstract class ModernTimeoutFlowUseCase<P, R>(
    // 🧹 HARDCODE CLEANUP: 使用AiConfig统一管理超时配置
    private val timeoutMs: Long = AiConfig.Timeouts.DEFAULT_REQUEST_TIMEOUT,
    private val dispatcher: CoroutineDispatcher,
    val logger: Logger,
) {
    /**
     * 核心抽象方法，子类需要实现此方法提供具体业务逻辑
     * @param params 输入参数
     * @return 包含结果的Flow
     */
    protected abstract suspend fun createFlowImpl(params: P): Flow<R>

    /**
     * 执行用例，适用于单次执行的场景
     * @param params 输入参数
     * @return 包含结果的ModernResult流
     */
    fun execute(params: P): Flow<ModernResult<R>> =
        flow {
            logger.d("开始执行带超时的Flow UseCase，超时时间：%d毫秒", timeoutMs)

            // 使用超时机制包装业务逻辑
            val result =
                withTimeout(timeoutMs) {
                    withContext(dispatcher) {
                        createFlowImpl(params)
                    }
                }

            // 将业务结果发射为成功的ModernResult
            result.collect { value ->
                emit(ModernResult.success(value))
            }
        }.catch { e ->
            // ✅ 使用Flow.catch{}操作符处理异常，符合Flow异常透明性原则
            when (e) {
                is TimeoutCancellationException -> {
                    logger.e(e, "Flow UseCase执行超时")
                    emit(
                        ModernResult.error(
                            ModernDataError(
                                operationName = "ModernTimeoutFlowUseCase.execute",
                                errorType = GlobalErrorType.Network.Timeout,
                                category = ErrorCategory.NETWORK,
                                uiMessage =
                                UiText.DynamicString(
                                    "Operation timed out, please try again later",
                                ),
                                severity = ErrorSeverity.ERROR,
                                cause = e,
                            ),
                        ),
                    )
                }

                is CancellationException -> {
                    logger.w("执行Flow被取消")
                    emit(
                        ModernResult.error(
                            ModernDataError(
                                operationName = "ModernTimeoutFlowUseCase.execute",
                                errorType = GlobalErrorType.System.General,
                                category = ErrorCategory.SYSTEM,
                                uiMessage = UiText.DynamicString("操作已取消"),
                                severity = ErrorSeverity.INFO,
                                metadataMap = mapOf("resource" to "Operation"),
                            ),
                        ),
                    )
                }

                else -> {
                    logger.e(e, "Flow执行异常: %s", e.message)
                    emit(
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "ModernTimeoutFlowUseCase.execute",
                                errorType = GlobalErrorType.System.General,
                                category = ErrorCategory.SYSTEM,
                                uiMessage = UiText.DynamicString("Flow UseCase执行失败"),
                                severity = ErrorSeverity.ERROR,
                                cause = e,
                            ),
                        ),
                    )
                }
            }
        }.flowOn(dispatcher)

    /**
     * 执行用例，适用于单次执行并获取单个结果的场景
     * @param params 输入参数
     * @return 单个结果的ModernResult
     */
    suspend fun executeForSingleResult(params: P): ModernResult<R> =
        try {
            logger.d("开始执行带超时的Flow UseCase获取单个结果，超时时间：%d毫秒", timeoutMs)

            // 使用超时机制包装业务逻辑
            val result =
                withTimeout(timeoutMs) {
                    withContext(dispatcher) {
                        createFlowImpl(params).first()
                    }
                }

            ModernResult.success(result)
        } catch (e: Exception) {
            when (e) {
                is TimeoutCancellationException -> {
                    logger.e(e, "获取单个结果超时")
                    ModernResult.error(
                        ModernDataError(
                            operationName = "ModernTimeoutFlowUseCase.executeForSingleResult",
                            errorType = GlobalErrorType.Network.Timeout,
                            category = ErrorCategory.NETWORK,
                            uiMessage = UiText.DynamicString("Operation timed out, please try again later"),
                            severity = ErrorSeverity.ERROR,
                            cause = e,
                        ),
                    )
                }

                is NoSuchElementException -> {
                    logger.e(e, "Flow为空，无法获取结果")
                    ModernResult.error(
                        ModernDataError(
                            operationName = "ModernTimeoutFlowUseCase.executeForSingleResult",
                            errorType = GlobalErrorType.Data.NotFound,
                            category = ErrorCategory.DATA,
                            uiMessage = UiText.DynamicString("No data available"),
                            severity = ErrorSeverity.WARNING,
                            cause = e,
                        ),
                    )
                }

                is CancellationException -> {
                    logger.w("执行Flow被取消")
                    ModernResult.error(
                        ModernDataError(
                            operationName = "ModernTimeoutFlowUseCase.executeForSingleResult",
                            errorType = GlobalErrorType.System.General,
                            category = ErrorCategory.SYSTEM,
                            uiMessage = UiText.DynamicString("操作已取消"),
                            severity = ErrorSeverity.INFO,
                            metadataMap = mapOf("resource" to "Operation"),
                        ),
                    )
                }

                else -> {
                    logger.e(e, "获取单个结果异常: %s", e.message)
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "ModernTimeoutFlowUseCase.executeForSingleResult",
                            errorType = GlobalErrorType.System.General,
                            category = ErrorCategory.SYSTEM,
                            uiMessage = UiText.DynamicString("获取数据失败"),
                            severity = ErrorSeverity.ERROR,
                            cause = e,
                        ),
                    )
                }
            }
        }
}

/**
 * 无参数版本的ModernTimeoutFlowUseCase
 */
abstract class ModernTimeoutFlowUseCaseNoParams<R>(
    timeoutMs: Long = 10000,
    dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernTimeoutFlowUseCase<Unit, R>(timeoutMs, dispatcher, logger) {
    /**
     * 无参数调用
     * @return 包含操作结果的Flow
     */
    operator fun invoke(): Flow<ModernResult<R>> = execute(Unit)

    /**
     * 无参数执行
     */
    abstract override suspend fun createFlowImpl(params: Unit): Flow<R>
}
