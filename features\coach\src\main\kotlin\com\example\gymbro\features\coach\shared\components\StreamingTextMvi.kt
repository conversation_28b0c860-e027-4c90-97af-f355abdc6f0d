package com.example.gymbro.features.coach.shared.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.coachTheme // 🔥 【主题修复】添加Coach主题导入
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

// ===== MVI Contract =====

/**
 * StreamingText组件的MVI Contract
 * 基于Profile模块成功实践的Contract模式
 */
object StreamingTextContract {
    /**
     * Intent - 用户意图
     */
    sealed interface Intent {
        /** 跳过动画 */
        data object SkipAnimation : Intent

        /** 暂停/恢复动画 */
        data object TogglePause : Intent

        /** 重置动画 */
        data object ResetAnimation : Intent

        /** 动画完成 */
        data object AnimationCompleted : Intent
    }

    /**
     * State - 流式文本状态
     */
    data class State(
        val displayedText: String = "",
        val isAnimating: Boolean = false,
        val isPaused: Boolean = false,
        val isCompleted: Boolean = false,
        val currentIndex: Int = 0,
        val totalLength: Int = 0,
    )

    /**
     * Effect - 一次性副作用
     */
    sealed interface Effect {
        /** 动画完成 */
        data object AnimationCompleted : Effect

        /** 动画跳过 */
        data object AnimationSkipped : Effect
    }
}

// ===== 默认配置 =====

/**
 * StreamingText默认配置
 */
object StreamingTextDefaults {
    const val DEFAULT_TYPING_SPEED = 35 // 毫秒
    const val FAST_TYPING_SPEED = 15
    const val SLOW_TYPING_SPEED = 80
}

// ===== MVI Composable =====

/**
 * StreamingTextMvi 组件 - 基于统一实现的MVI封装
 *
 * 核心特性：
 * - 打字机效果：逐字符显示文本
 * - 金属质感：可选的渐变色彩效果
 * - 动画控制：支持暂停、跳过、重置
 * - 性能优化：使用Flow进行文本流处理
 * - 主题集成：完全集成GymBro主题系统
 *
 * 🔥 重构说明：
 * - 内部使用 UnifiedStreamingText 实现核心功能
 * - 保持原有的 MVI 接口不变，确保向后兼容
 * - 提供更好的性能和一致性
 *
 * @param textFlow 文本流
 * @param style 文本样式
 * @param typingSpeed 打字速度（毫秒）
 * @param enableMetallicEffect 是否启用金属质感效果
 * @param useHdr 是否使用HDR色彩
 * @param skipEnabled 是否允许跳过动画（暂未实现）
 * @param onIntent Intent回调（稳定Lambda）
 * @param modifier 修饰符
 */
@Composable
fun StreamingTextMvi(
    textFlow: Flow<String>,
    style: TextStyle = MaterialTheme.typography.bodyMedium,
    typingSpeed: Int = StreamingTextDefaults.DEFAULT_TYPING_SPEED,
    enableMetallicEffect: Boolean = false,
    useHdr: Boolean = false,
    skipEnabled: Boolean = true,
    onIntent: (StreamingTextContract.Intent) -> Unit = {},
    modifier: Modifier = Modifier,
) {
    // 🔥 使用统一的流式文本组件
    UnifiedStreamingText(
        textFlow = textFlow,
        modifier = modifier,
        style = style,
        typingSpeed = typingSpeed.toLong(),
        enableMetallicEffect = enableMetallicEffect,
        useHdr = useHdr,
        onAnimationComplete = {
            onIntent(StreamingTextContract.Intent.AnimationCompleted)
        },
    )
}

/**
 * 创建金属质感渐变画刷
 */
@Composable
private fun createMetallicBrush(
    alpha: Float,
    useHdr: Boolean,
): Brush {
    val colors =
        if (useHdr) {
            // 🧹 HARDCODE CLEANUP: 使用主题颜色替代硬编码HDR色彩
            listOf(
                MaterialTheme.coachTheme.accentPrimary.copy(alpha = alpha * 1.2f), // 增强金色效果
                MaterialTheme.coachTheme.accentSecondary.copy(alpha = alpha * 1.1f), // 增强橙色效果
                MaterialTheme.coachTheme.accentCTA.copy(alpha = alpha), // 红色调
                MaterialTheme.coachTheme.accentPrimary.copy(alpha = alpha * 1.2f), // 回到金色
            )
        } else {
            // 标准色彩 - 🔥 【主题修复】使用Coach主题颜色
            listOf(
                MaterialTheme.coachTheme.accentPrimary.copy(alpha = alpha),
                MaterialTheme.coachTheme.accentSecondary.copy(alpha = alpha),
                MaterialTheme.coachTheme.accentCTA.copy(alpha = alpha),
                MaterialTheme.coachTheme.accentPrimary.copy(alpha = alpha),
            )
        }

    return Brush.linearGradient(colors)
}

/**
 * 简化版StreamingText - 用于快速集成
 */
@Composable
fun StreamingText(
    textFlow: Flow<String>,
    style: TextStyle = MaterialTheme.typography.bodyMedium,
    typingSpeed: Int = StreamingTextDefaults.DEFAULT_TYPING_SPEED,
    enableMetallicEffect: Boolean = false,
    modifier: Modifier = Modifier,
) {
    StreamingTextMvi(
        textFlow = textFlow,
        style = style,
        typingSpeed = typingSpeed,
        enableMetallicEffect = enableMetallicEffect,
        onIntent = { /* 简化版本不处理Intent */ },
        modifier = modifier,
    )
}

// ===== Preview =====

@GymBroPreview
@Composable
private fun StreamingTextMviPreview() {
    GymBroTheme {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Text(
                text = "普通流式文本：",
                style = MaterialTheme.typography.labelMedium,
                modifier = Modifier.padding(bottom = 8.dp),
            )

            StreamingTextMvi(
                textFlow = flowOf("这是一段流式显示的文本，模拟AI回复的打字机效果。"),
                style = MaterialTheme.typography.bodyMedium,
                typingSpeed = 50,
                modifier = Modifier.padding(bottom = 16.dp),
            )

            Text(
                text = "金属质感流式文本：",
                style = MaterialTheme.typography.labelMedium,
                modifier = Modifier.padding(bottom = 8.dp),
            )

            StreamingTextMvi(
                textFlow = flowOf("这是一段带有金属质感效果的流式文本，展示了高级的视觉效果。"),
                style = MaterialTheme.typography.bodyMedium,
                typingSpeed = 40,
                enableMetallicEffect = true,
                modifier = Modifier.padding(bottom = 16.dp),
            )

            Text(
                text = "HDR金属质感：",
                style = MaterialTheme.typography.labelMedium,
                modifier = Modifier.padding(bottom = 8.dp),
            )

            StreamingTextMvi(
                textFlow = flowOf("这是HDR色彩的金属质感文本，提供更丰富的视觉体验。"),
                style = MaterialTheme.typography.bodyLarge,
                typingSpeed = 30,
                enableMetallicEffect = true,
                useHdr = true,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun StreamingTextSimplePreview() {
    GymBroTheme {
        StreamingText(
            textFlow = flowOf("简化版本的流式文本组件"),
            style =
            MaterialTheme.typography.bodyMedium.copy(
                textAlign = TextAlign.Center,
            ),
            enableMetallicEffect = true,
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(16.dp),
        )
    }
}
