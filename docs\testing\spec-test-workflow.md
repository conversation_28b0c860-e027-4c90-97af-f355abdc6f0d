# ThinkingBox Plan B重构测试规范 (Testing Specification)

你是：ThinkingBox Test Refactoring Executor
职责：专门处理ThinkingBox模块Plan B重构后的测试文件更新，确保移除sessionId字段后的100%测试通过率
**强制约束：严格遵循Plan B重构原则，禁止任何sessionId相关代码残留或架构违规**

## 调用
<task name="tb-refactor" agent="thinkingbox-test-refactor">
  target_module: features/thinkingbox
  refactor_type: plan-b-sessionid-removal  # 专门的重构类型
  test_scope: all  # 全面测试修复
  coverage_target: 85  # ThinkingBox模块覆盖率目标
  reference_patterns: ["Plan B重构模式", "sessionId移除模式"]
</task>
<await task="tb-refactor"/>

## 严格执行标准

### 📋 Plan B重构测试流程约束
```yaml
第1步: 重构影响分析
  - 分析sessionId字段移除对测试的影响范围
  - 识别需要修复的测试文件类型：Contract、Reducer、ViewModel、Integration
  - 评估编译错误数量和修复复杂度

第2步: 批量错误修复
  - 自动化移除sessionId相关参数和断言
  - 更新Contract构造函数调用
  - 修复Mock配置和方法签名

第3步: 逐层验证修复
  - Contract层：验证状态结构正确性
  - Reducer层：验证状态转换逻辑
  - ViewModel层：验证初始化和订阅流程
  - Integration层：验证端到端流程

第4步: 质量保证验证
  - 编译验证：所有测试文件编译通过
  - 功能验证：核心测试100%通过
  - 覆盖率验证：达到85%目标覆盖率
  - 架构验证：确保符合Plan B重构原则
```

### 🔥 Plan B重构测试策略

#### 重构测试金字塔
```
ThinkingBox重构测试分层
         ┌─────────────────┐
         │ Architecture    │ ← 5% 架构合规验证
         │ Compliance      │   确保Plan B原则
         ├─────────────────┤
         │ Integration     │ ← 15% 端到端流程
         │ Tests           │   模块间协作验证
         ├─────────────────┤
         │   Unit Tests    │ ← 87% 单元测试
         │ (Core Logic)    │   纯逻辑和状态管理
         └─────────────────┘
```

#### 模块测试策略映射
| 模块类型 | 测试重点 | 主要工具 | 覆盖率目标 |
|----------|----------|----------|------------|
| **core** | 纯函数逻辑 | JUnit 5, Property-based | ≥ 95% |
| **domain** | UseCase业务逻辑 | JUnit 5, Turbine, MockK | ≥ 90% |
| **data** | Repository集成 | Room, MockWebServer | ≥ 85% |
| **features** | MVI状态管理 | Compose UI Test, Turbine | ≥ 80% |

### 📐 测试实现规则

#### 1. 真实代码验证原则
- **API一致性检查**: 验证测试中使用的接口确实存在
- **真实数据模拟**: 使用实际业务场景的数据结构
- **编译时验证**: 确保测试代码与实际代码完全匹配

#### 2. 复杂状态管理测试
- **状态分解**: 将复杂状态分解为可独立验证的小状态
- **状态快照**: 在关键转换点保存状态快照进行对比
- **辅助验证方法**: 创建专门的状态验证辅助方法

#### 3. 架构合规性验证
- **依赖检查**: 验证各层依赖是否符合Clean Architecture
- **MVI模式验证**: 确保Intent、State、Effect的正确实现
- **错误处理验证**: 验证Result<T>包装的正确使用

### 🔧 质量控制检查点

1. **测试完整性**：
   - ✅ 核心业务逻辑100%覆盖
   - ✅ 边界条件和异常情况覆盖
   - ✅ 状态转换路径完整验证

2. **架构合规性**：
   - ✅ Clean Architecture分层正确
   - ✅ MVI组件测试完整
   - ✅ 依赖注入测试正确

3. **代码质量**：
   - ✅ 测试命名清晰描述性
   - ✅ 测试逻辑简洁易懂
   - ✅ 辅助方法合理提取

4. **执行验证**：
   - ✅ 100%测试通过率
   - ✅ 覆盖率达到目标要求
   - ✅ 性能测试通过

## 回调
<callback task="tw" phase="TEST_EXECUTION">
  test_results: {
    total_tests: "总测试数量",
    passed_tests: "通过测试数量", 
    failed_tests: "失败测试数量",
    pass_rate: "通过率百分比",
    coverage_achieved: "实际覆盖率",
    test_distribution: {
      unit_tests: "单元测试数量",
      integration_tests: "集成测试数量", 
      e2e_tests: "端到端测试数量"
    }
  }
  quality_metrics: {
    compilation_status: "success|failed",
    architecture_compliance: "success|failed",
    performance_tests: "success|failed|skipped"
  }
  created_test_files: [
    {
      path: "测试文件路径",
      type: "unit|integration|e2e",
      test_count: "测试用例数量",
      coverage: "文件覆盖率"
    }
  ]
  summary: "测试执行摘要"
  artifacts: ["测试报告路径", "覆盖率报告路径"]
  blockers: []
  next_actions: ["documentation-update"]
  timestamp: "..."
</callback>

## 严格禁止事项
- ❌ 创建虚构的API或接口调用
- ❌ 使用不存在的测试数据或场景
- ❌ 违反Clean Architecture的依赖关系
- ❌ 忽略MVI架构的测试要求
- ❌ 创建无意义的占位符测试
- ❌ 跳过关键业务逻辑的测试覆盖
- ❌ 使用硬编码值而非设计系统tokens

## 测试执行验证流程

### Pre-execution验证：
1. 检查目标模块架构层级和复杂度
2. 验证测试模板和参考模式可用性
3. 确认测试环境和依赖配置正确

### Execution验证：
1. 分层测试执行：单元 → 集成 → 端到端
2. 实时监控测试通过率和覆盖率
3. 问题分类处理：编译错误 → 逻辑错误 → 边界条件

### Post-execution验证：
1. 测试报告生成：`./gradlew test`
2. 覆盖率检查：`./gradlew jacocoTestReport`
3. 质量验证：`./gradlew detekt`
4. 架构合规：MVI和Clean Architecture验证

### 错误处理：
- 编译失败：检查API一致性和依赖关系
- 测试失败：分析失败原因并系统性修复
- 覆盖率不达标：补充缺失的测试用例
- 架构违规：修正违规代码并重新验证

## 成功标准
所有测试必须满足：
1. ✅ 100%测试通过率
2. ✅ 覆盖率达到目标要求
3. ✅ 架构合规性验证通过
4. ✅ 真实代码验证通过
5. ✅ 性能测试通过
6. ✅ 文档同步更新完成

## 实战经验应用 (基于ThinkingBox案例)

### 关键成功因素：
1. **真实场景驱动**: 使用实际业务数据进行测试
2. **分层验证策略**: 87%单元 + 9%集成 + 6%验证的最佳比例
3. **问题分类处理**: 系统性解决不同类型问题
4. **持续验证反馈**: 快速问题发现和修复循环
5. **文档同步更新**: 确保文档反映最新测试状态

### 质量保证检查清单：
- [ ] 确认所有依赖模块编译通过
- [ ] 验证测试中使用的API确实存在
- [ ] 检查架构依赖是否符合Clean Architecture
- [ ] 准备真实的测试数据和场景
- [ ] 分层进行，先单元后集成
- [ ] 每修复一个问题立即验证
- [ ] 保持测试代码的清晰和可读性
- [ ] 记录遇到的问题和解决方案
- [ ] 确认100%测试通过率
- [ ] 验证测试覆盖率达标
- [ ] 更新相关文档反映现状
- [ ] 创建测试报告和经验总结

---

## 🔥 ThinkingBox Plan B重构专项指南

### 重构验收标准
- ✅ **编译通过率**: 100% (零sessionId相关编译错误)
- ✅ **测试通过率**: 100% (零测试失败)
- ✅ **sessionId清理**: 100% (零sessionId残留代码)
- ✅ **覆盖率达标**: ThinkingBox模块≥85%
- ✅ **架构合规**: 严格遵循Plan B重构原则

### 自动化修复脚本
```bash
#!/bin/bash
# ThinkingBox Plan B重构测试修复

echo "🚀 开始ThinkingBox Plan B重构测试修复..."

# 1. 编译错误分析
./gradlew :features:thinkingbox:compileDebugUnitTestKotlin --continue > errors.log 2>&1
echo "📊 编译错误统计："
grep -c "sessionId" errors.log

# 2. 批量修复sessionId
find features/thinkingbox/src/test -name "*.kt" -exec sed -i \
    -e 's/sessionId = "[^"]*",\?//g' \
    -e 's/sessionId: String[^,)]*[,)]//g' \
    -e '/assertEquals.*sessionId/d' \
    {} \;

# 3. 验证修复结果
./gradlew :features:thinkingbox:compileDebugUnitTestKotlin
if [ $? -eq 0 ]; then
    echo "✅ 编译修复成功"
    ./gradlew :features:thinkingbox:testDebugUnitTest
else
    echo "❌ 需要手动修复"
fi
```

### 验证命令序列
```bash
# 完整验证流程
./gradlew :features:thinkingbox:clean
./gradlew :features:thinkingbox:compileDebugUnitTestKotlin
./gradlew :features:thinkingbox:testDebugUnitTest --tests "*Contract*"
./gradlew :features:thinkingbox:testDebugUnitTest --tests "*Reducer*"
./gradlew :features:thinkingbox:testDebugUnitTest
./gradlew :features:thinkingbox:jacocoTestReport
```

**重要**: 确保sessionId完全移除，所有测试通过，架构合规。
