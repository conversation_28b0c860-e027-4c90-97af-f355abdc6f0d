package com.example.gymbro.features.profile.internal.presentation.contract

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText

/**
 * Invite功能的MVI契约
 * 轻量级实现，为未来扩展做准备
 */
internal object InviteContract {

    /**
     * Invite页面的UI状态
     */
    @Immutable
    data class State(
        val isLoading: Boolean = false,
        val inviteCode: String = "",
        val shareUrl: String = "",
        val error: UiText? = null,
        val shareSuccess: Boolean = false,
    ) : UiState

    /**
     * 用户意图/动作 - 🧹 MVI COMPLIANCE: 符合黄金标准的Intent命名
     */
    sealed class Intent : AppIntent {
        // 🧹 MVI COMPLIANCE: Intent必须以动词开头，表达用户意图
        object LoadInviteData : Intent()
        object GenerateInviteCode : Intent()
        object ShareInvite : Intent()
        object ClearError : Intent()
        object ClearShareSuccess : Intent()
    }

    /**
     * 副作用
     */
    sealed class Effect : UiEffect {
        data class ShowSnackbar(val message: UiText) : Effect()
        object NavigateBack : Effect()
        data class ShareContent(val content: String) : Effect()
        object ShowShareDialog : Effect()
    }
}
