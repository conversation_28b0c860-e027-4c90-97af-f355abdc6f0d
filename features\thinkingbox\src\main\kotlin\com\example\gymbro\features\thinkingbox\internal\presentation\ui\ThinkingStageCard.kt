package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.components.text.AnimationIntensity
import com.example.gymbro.designSystem.components.text.RenderMode
import com.example.gymbro.designSystem.components.text.RenderSpeed
import com.example.gymbro.designSystem.components.text.TextRenderingConfig
import com.example.gymbro.designSystem.components.text.UnifiedTextRenderer
import com.example.gymbro.designSystem.theme.tokens.MapleMono
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import timber.log.Timber

/**
 * ThinkingStageCard - P0核心架构重构版本（四条铁律）
 *
 * 🔥 【四条铁律实现】:
 * 1. UI绝对不重组刷新 - 使用UnifiedTextRenderer的单一Text组件
 * 2. 优雅的1秒30字符显示 - 精确33ms/字符延迟
 * 3. 思考框硬上限1/3屏高 - 由外层容器控制
 * 4. 文本内容8行超限省略 - ✅ maxLines=8 + TextOverflow.Ellipsis 已实现
 *
 * 🎯 【独立渲染生命周期】:
 * - 每个ThinkingStageCard管理自己的渲染状态
 * - 精确打字机效果和8行截断逻辑
 * - UI握手机制：渲染完成后调用onSegmentRendered
 */
@Composable
fun ThinkingStageCard(
    segment: ThinkingBoxContract.SegmentUi,
    isActive: Boolean = true,
    modifier: Modifier = Modifier,
    onSegmentRendered: ((String) -> Unit)? = null,
) {
    // 🔥 【1/3屏高度限制】四条铁律第3条
    val maxHeight = (LocalConfiguration.current.screenHeightDp * 0.33f).dp

    // 🔥 【独立渲染状态】跟踪标题和内容渲染状态
    var titleRenderComplete by remember(segment.id) { mutableStateOf(false) }
    var contentRenderComplete by remember(segment.id) { mutableStateOf(false) }

    // 🔥 【UI握手机制】整个段渲染完成后触发回调
    LaunchedEffect(titleRenderComplete, contentRenderComplete, segment.id) {
        val titleHasContent = !segment.title.isNullOrBlank()
        val contentHasContent = segment.content.isNotEmpty()

        val shouldReportComplete = when {
            titleHasContent && contentHasContent -> titleRenderComplete && contentRenderComplete
            titleHasContent && !contentHasContent -> titleRenderComplete
            !titleHasContent && contentHasContent -> contentRenderComplete
            else -> true // 无内容时立即完成
        }

        if (shouldReportComplete) {
            Timber.tag("TB-RENDER").d("✅ [段渲染完成] ${segment.id}")
            onSegmentRendered?.invoke(segment.id)
        }
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(max = maxHeight), // 🔥 【四条铁律第3条】硬上限
        shape = RoundedCornerShape(Tokens.Radius.Large),
        elevation = CardDefaults.cardElevation(Tokens.Elevation.None),
        colors = CardDefaults.cardColors(
            containerColor = Tokens.Color.Gray200,
        ),
    ) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Large),
        ) {
            // 🔥 【标题显示】基于SegmentUi的标题显示逻辑
            val displayTitle = when {
                segment.kind == SegmentKind.PERTHINK -> ThinkingBoxStrings.PERTHINK_TITLE
                !segment.title.isNullOrBlank() -> segment.title
                else -> null
            }

            Timber.tag("TB-SEGMENT").d("🔥 [Segment显示] id=${segment.id}, kind=${segment.kind}")
            Timber.tag(
                "TB-SEGMENT",
            ).d("🔥 [Segment显示] title='${segment.title}', displayTitle='$displayTitle'")

            // 🔥 【标题渲染】使用designSystem.UnifiedTextRenderer
            displayTitle?.let { title ->
                UnifiedTextRenderer(
                    text = title,
                    config = TextRenderingConfig(
                        renderMode = RenderMode.TYPEWRITER,
                        renderSpeed = RenderSpeed.COMFORTABLE, // 33ms/字符 - 四条铁律
                        animationIntensity = AnimationIntensity.NONE,
                        style = TextStyle(
                            fontFamily = MapleMono,
                            fontSize = Tokens.Typography.Headline, // 20.sp
                            fontWeight = FontWeight.Bold,
                            color = Tokens.Color.Gray800,
                        ),
                    ),
                    onRenderComplete = {
                        titleRenderComplete = true
                        Timber.tag("TB-RENDER").d("✅ [标题渲染完成] ${segment.id}")
                    },
                    modifier = Modifier.fillMaxWidth(),
                )

                // 标题和内容间的间距
                Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))
            }

            // 🔥 【内容渲染】使用designSystem.UnifiedTextRenderer
            if (segment.content.isNotEmpty()) {
                UnifiedTextRenderer(
                    text = segment.content,
                    config = TextRenderingConfig(
                        renderMode = RenderMode.TYPEWRITER,
                        renderSpeed = RenderSpeed.COMFORTABLE, // 33ms/字符 - 四条铁律
                        animationIntensity = AnimationIntensity.NONE,
                        style = TextStyle(
                            fontFamily = MapleMono,
                            fontSize = Tokens.Typography.Body, // 14.sp
                            fontWeight = FontWeight.Normal,
                            lineHeight = Tokens.Typography.Body * 1.5f,
                            color = Tokens.Color.Gray800,
                        ),
                        // 🔥 【四条铁律第4条】文本内容8行超限省略
                        maxLines = 8,
                        overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis,
                    ),
                    onRenderComplete = {
                        contentRenderComplete = true
                        Timber.tag("TB-RENDER").d("✅ [内容渲染完成] ${segment.id}")
                    },
                    modifier = Modifier.fillMaxWidth(),
                )
            } else {
                // 无内容时立即标记完成
                LaunchedEffect(segment.id) {
                    contentRenderComplete = true
                }
            }
        }
    }

    // 🔥 【P0核心架构重构完成】独立渲染生命周期 + 四条铁律实现
}

// ==================== Preview Functions ====================

@Composable
private fun createMockSegmentUi(
    id: String,
    kind: SegmentKind,
    title: String?,
    content: String,
    isComplete: Boolean = true,
): ThinkingBoxContract.SegmentUi {
    return ThinkingBoxContract.SegmentUi(
        id = id,
        kind = kind,
        title = title,
        content = content,
        isComplete = isComplete,
    )
}

@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_Simple() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            segment = createMockSegmentUi(
                id = "preview-1",
                kind = SegmentKind.PHASE,
                title = "规划力量训练计划",
                content = "用户希望我帮助他们制定力量训练计划。我们可以运用一般的知识创建一般性的力量训练方案，考虑到用户的需求以及可能的训练场地，例如当地的健身房。当然，若要提供最新和特定的推荐，查询些最新的研究或指南，比如 ACSM 2023 的力量训练指引，也许能提高答案的可靠性。但如果没有具体需求，使用内部的通用建议也可能足够。",
            ),
        )
    }
}

@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_NoTitle() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            segment = createMockSegmentUi(
                id = "preview-2",
                kind = SegmentKind.PHASE,
                title = null,
                content = "这是一段没有标题的思考内容。内容应该直接显示而不会有额外的头部空间。",
            ),
        )
    }
}

@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_Perthink() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            segment = createMockSegmentUi(
                id = "preview-3",
                kind = SegmentKind.PERTHINK,
                title = null, // perthink会使用固定标题
                content = "用户想要制定力量训练计划，我需要考虑他们的具体需求和目标。",
            ),
        )
    }
}
