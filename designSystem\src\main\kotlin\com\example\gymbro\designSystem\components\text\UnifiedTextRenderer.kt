package com.example.gymbro.designSystem.components.text

import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.components.extras.MetallicText
import com.example.gymbro.designSystem.components.extras.STREAMING_ANIM_DURATION
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.coachTheme
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.motion.MotionEasings
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * 🚀 统一文本渲染器系统
 *
 * 基于designSystem的全新文本渲染架构，提供：
 * - 金属跃动标题效果
 * - 渐进式打字机显示
 * - 统一的动画和性能优化
 * - 完整的配置和扩展支持
 *
 * 设计原则：
 * - 60fps流畅动画保证
 * - 基于Tokens系统的一致性
 * - 防重组的性能优化
 * - 模块化和可扩展架构
 */

/**
 * 文本渲染模式枚举
 */
enum class RenderMode {
    INSTANT, // 瞬显模式 - PreThink使用
    TYPEWRITER, // 打字机模式 - 逐字符显示
    METALLIC_PULSE, // 金属跃动模式 - 标题特效
    STREAMING, // 流式模式 - AI响应显示
}

/**
 * 动画强度级别
 */
enum class AnimationIntensity {
    NONE, // 无动画
    LOW, // 低强度 - 轻微效果
    MEDIUM, // 中等强度 - 标准效果
    HIGH, // 高强度 - 完整特效
}

/**
 * 渲染速度预设
 */
enum class RenderSpeed {
    INSTANT(MotionDurations.Coach.TextRenderer.TYPEWRITER_INSTANT), // 立即显示
    FAST(MotionDurations.Coach.TextRenderer.TYPEWRITER_FAST), // 快速 - 8ms/字符
    SMOOTH(MotionDurations.Coach.TextRenderer.TYPEWRITER_SMOOTH), // 流畅 - 16ms/字符 (默认)
    COMFORTABLE(MotionDurations.Coach.TextRenderer.TYPEWRITER_COMFORTABLE), // 舒适 - 33ms/字符
    SLOW(MotionDurations.Coach.TextRenderer.TYPEWRITER_SLOW), // 慢速 - 50ms/字符
    ;

    val delayMs: Long

    constructor(delayMs: Long) {
        this.delayMs = delayMs
    }
}

/**
 * 文本渲染配置数据类
 */
data class TextRenderingConfig(
    val renderMode: RenderMode = RenderMode.TYPEWRITER,
    val animationIntensity: AnimationIntensity = AnimationIntensity.MEDIUM,
    val renderSpeed: RenderSpeed = RenderSpeed.SMOOTH,
    val enableMetallic: Boolean = false,
    val enableHdr: Boolean = false,
    val fontWeight: FontWeight = FontWeight.Normal,
    val style: TextStyle? = null,
    val animationDurationMs: Int = MotionDurations.Coach.TextRenderer.METALLIC_PULSE_NORMAL,
    // 🔥 【8行截断支持】新增maxLines和overflow参数
    val maxLines: Int = Int.MAX_VALUE,
    val overflow: androidx.compose.ui.text.style.TextOverflow = androidx.compose.ui.text.style.TextOverflow.Clip,
)

/**
 * 🌟 统一文本渲染器核心组件
 *
 * 所有文本渲染的统一入口，根据配置自动选择最佳渲染方式
 *
 * @param text 要显示的文本内容
 * @param config 渲染配置
 * @param modifier 修饰符
 * @param onRenderComplete 渲染完成回调
 */
@Composable
fun UnifiedTextRenderer(
    text: String,
    config: TextRenderingConfig = TextRenderingConfig(),
    modifier: Modifier = Modifier,
    onRenderComplete: () -> Unit = {},
) {
    when (config.renderMode) {
        RenderMode.INSTANT -> {
            InstantTextRenderer(
                text = text,
                config = config,
                modifier = modifier,
                onComplete = onRenderComplete,
            )
        }
        RenderMode.TYPEWRITER -> {
            TypewriterTextRenderer(
                text = text,
                config = config,
                modifier = modifier,
                onComplete = onRenderComplete,
            )
        }
        RenderMode.METALLIC_PULSE -> {
            MetallicPulseTextRenderer(
                text = text,
                config = config,
                modifier = modifier,
                onComplete = onRenderComplete,
            )
        }
        RenderMode.STREAMING -> {
            StreamingTextRenderer(
                text = text,
                config = config,
                modifier = modifier,
                onComplete = onRenderComplete,
            )
        }
    }
}

/**
 * ⚡ 瞬显文本渲染器
 * 用于PreThink阶段的即时显示
 */
@Composable
private fun InstantTextRenderer(
    text: String,
    config: TextRenderingConfig,
    modifier: Modifier = Modifier,
    onComplete: () -> Unit = {},
) {
    LaunchedEffect(text) {
        onComplete()
    }

    if (config.enableMetallic) {
        MetallicText(
            text = text,
            modifier = modifier,
            animated = config.animationIntensity != AnimationIntensity.NONE,
            useHdr = config.enableHdr,
            animationDurationMs = config.animationDurationMs,
            fontWeight = config.fontWeight,
            style = config.style ?: MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.coachTheme.textSecondary,
            ),
            // 🔥 【8行截断支持】传递maxLines和overflow参数
            maxLines = config.maxLines,
            overflow = config.overflow,
        )
    } else {
        Text(
            text = text,
            modifier = modifier,
            style = config.style ?: MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.coachTheme.textSecondary,
            ),
            // 🔥 【8行截断支持】添加maxLines和overflow参数
            maxLines = config.maxLines,
            overflow = config.overflow,
        )
    }
}

/**
 * ⌨️ 打字机文本渲染器
 * 经典的逐字符显示效果，优化了性能和流畅度
 * 🔥 支持追加形式文本渲染 - 符合ThinkingBox要求
 */
@Composable
private fun TypewriterTextRenderer(
    text: String,
    config: TextRenderingConfig,
    modifier: Modifier = Modifier,
    onComplete: () -> Unit = {},
) {
    // 🔥 【追加形式渲染】使用稳定的key，避免重复渲染
    var displayedText by remember(text.take(0)) { mutableStateOf("") }
    var isComplete by remember(text.take(0)) { mutableStateOf(false) }

    LaunchedEffect(text, config.renderSpeed) {
        if (text.isEmpty()) {
            displayedText = ""
            isComplete = true
            onComplete()
            return@LaunchedEffect
        }

        // 🔥 【追加形式核心逻辑】只追加新内容，不重复播放已有内容
        if (text.startsWith(displayedText)) {
            // 增量追加新内容
            val startIndex = displayedText.length
            val characters = text.toCharArray()

            for (i in startIndex until characters.size) {
                if (!isActive) return@LaunchedEffect

                displayedText = String(characters, 0, i + 1)

                // 最后一个字符不需要延迟，立即完成
                if (i < characters.size - 1) {
                    delay(config.renderSpeed.delayMs)
                }
            }
        } else {
            // 🔥 【异常情况处理】文本内容完全变化时重新开始
            displayedText = ""
            isComplete = false

            val characters = text.toCharArray()
            for (i in characters.indices) {
                if (!isActive) return@LaunchedEffect

                displayedText = String(characters, 0, i + 1)

                if (i < characters.size - 1) {
                    delay(config.renderSpeed.delayMs)
                }
            }
        }

        // 🔥 【回调动画机制】确保打字机完成后触发回调
        if (!isComplete) {
            isComplete = true
            onComplete()
        }
    }

    if (config.enableMetallic) {
        MetallicText(
            text = displayedText,
            modifier = modifier,
            animated = config.animationIntensity != AnimationIntensity.NONE,
            useHdr = config.enableHdr,
            animationDurationMs = config.animationDurationMs,
            fontWeight = config.fontWeight,
            style = config.style ?: MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.coachTheme.textPrimary,
            ),
            // 🔥 【8行截断支持】传递maxLines和overflow参数
            maxLines = config.maxLines,
            overflow = config.overflow,
        )
    } else {
        Text(
            text = displayedText,
            modifier = modifier,
            style = config.style ?: MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.coachTheme.textPrimary,
            ),
            // 🔥 【8行截断支持】添加maxLines和overflow参数
            maxLines = config.maxLines,
            overflow = config.overflow,
        )
    }
}

/**
 * 🌟 金属跃动文本渲染器
 * 专为标题设计的高级视觉效果
 */
@Composable
private fun MetallicPulseTextRenderer(
    text: String,
    config: TextRenderingConfig,
    modifier: Modifier = Modifier,
    onComplete: () -> Unit = {},
) {
    var displayedText by remember(text) { mutableStateOf("") }

    // 脉冲动画
    val pulseScale by if (config.animationIntensity != AnimationIntensity.NONE) {
        rememberInfiniteTransition(label = "metallic_pulse").animateFloat(
            initialValue = 0.98f,
            targetValue = 1.02f,
            animationSpec = infiniteRepeatable(
                animation = tween(
                    durationMillis = config.animationDurationMs,
                    easing = MotionEasings.DECORATIVE,
                ),
                repeatMode = RepeatMode.Reverse,
            ),
            label = "pulse_scale",
        )
    } else {
        remember { mutableStateOf(1f) }
    }

    // 文本显示逻辑
    LaunchedEffect(text, config.renderSpeed) {
        if (config.renderSpeed == RenderSpeed.INSTANT) {
            displayedText = text
            onComplete()
        } else {
            // 渐进式显示
            if (text.startsWith(displayedText)) {
                val startIndex = displayedText.length
                for (i in startIndex until text.length) {
                    if (!isActive) return@LaunchedEffect
                    displayedText = text.substring(0, i + 1)
                    if (i < text.length - 1) {
                        delay(config.renderSpeed.delayMs)
                    }
                }
            } else {
                displayedText = ""
                for (i in text.indices) {
                    if (!isActive) return@LaunchedEffect
                    displayedText = text.substring(0, i + 1)
                    delay(config.renderSpeed.delayMs)
                }
            }
            onComplete()
        }
    }

    MetallicText(
        text = displayedText,
        modifier = modifier.graphicsLayer {
            if (config.animationIntensity != AnimationIntensity.NONE) {
                scaleX = pulseScale
                scaleY = pulseScale
            }
        },
        animated = true,
        useHdr = config.enableHdr,
        animationDurationMs = config.animationDurationMs,
        fontWeight = config.fontWeight,
        style = config.style ?: MaterialTheme.typography.titleMedium.copy(
            fontSize = 17.sp,
            letterSpacing = 0.3.sp,
            color = MaterialTheme.coachTheme.textPrimary,
        ),
        // 🔥 【8行截断支持】传递maxLines和overflow参数
        maxLines = config.maxLines,
        overflow = config.overflow,
    )
}

/**
 * 🌊 流式文本渲染器
 * 专为AI响应设计的流畅显示效果
 */
@Composable
private fun StreamingTextRenderer(
    text: String,
    config: TextRenderingConfig,
    modifier: Modifier = Modifier,
    onComplete: () -> Unit = {},
) {
    var displayedText by remember(text) { mutableStateOf("") }

    LaunchedEffect(text, config.renderSpeed) {
        if (text.isEmpty()) {
            displayedText = ""
            onComplete()
            return@LaunchedEffect
        }

        // 批量处理优化 - 长文本使用批量渲染
        val batchSize = when {
            text.length > 1000 -> 10
            text.length > 200 -> 5
            else -> 1
        }

        if (text.startsWith(displayedText)) {
            val newContent = text.substring(displayedText.length)
            for (i in newContent.indices step batchSize) {
                if (!isActive) return@LaunchedEffect

                val endIndex = minOf(i + batchSize, newContent.length)
                val batch = newContent.substring(i, endIndex)
                displayedText += batch

                if (endIndex < newContent.length) {
                    delay(config.renderSpeed.delayMs)
                }
            }
        } else {
            displayedText = ""
            for (i in text.indices step batchSize) {
                if (!isActive) return@LaunchedEffect

                val endIndex = minOf(i + batchSize, text.length)
                displayedText = text.substring(0, endIndex)

                if (endIndex < text.length) {
                    delay(config.renderSpeed.delayMs)
                }
            }
        }

        onComplete()
    }

    if (config.enableMetallic) {
        MetallicText(
            text = displayedText,
            modifier = modifier,
            animated = config.animationIntensity != AnimationIntensity.NONE,
            useHdr = config.enableHdr,
            animationDurationMs = STREAMING_ANIM_DURATION,
            fontWeight = config.fontWeight,
            style = config.style ?: MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.coachTheme.textPrimary,
            ),
            // 🔥 【8行截断支持】传递maxLines和overflow参数
            maxLines = config.maxLines,
            overflow = config.overflow,
        )
    } else {
        Text(
            text = displayedText,
            modifier = modifier,
            style = config.style ?: MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.coachTheme.textPrimary,
            ),
            // 🔥 【8行截断支持】添加maxLines和overflow参数
            maxLines = config.maxLines,
            overflow = config.overflow,
        )
    }
}

/**
 * 🎯 便捷函数：静态流式文本
 * ThinkingBox标题的专用组件 - 移除动画效果
 */
@Composable
fun MetallicStreamingText(
    text: String,
    modifier: Modifier = Modifier,
    animationIntensity: AnimationIntensity = AnimationIntensity.NONE, // 🔥 【关键修复】默认无动画
    renderSpeed: RenderSpeed = RenderSpeed.SMOOTH,
    fontWeight: FontWeight = FontWeight.SemiBold,
    onComplete: () -> Unit = {},
) {
    UnifiedTextRenderer(
        text = text,
        config = TextRenderingConfig(
            renderMode = RenderMode.TYPEWRITER, // 🔥 【关键修复】使用普通打字机模式，移除金属脉冲
            animationIntensity = AnimationIntensity.NONE, // 🔥 【关键修复】禁用所有动画效果
            renderSpeed = renderSpeed,
            enableMetallic = false, // 🔥 【关键修复】禁用金属效果
            enableHdr = false,
            fontWeight = fontWeight,
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = fontWeight,
                color = MaterialTheme.coachTheme.textPrimary,
            ),
        ),
        modifier = modifier,
        onRenderComplete = onComplete,
    )
}

/**
 * 📝 便捷函数：渐进式文本渲染器
 * 内容显示的专用组件
 */
@Composable
fun ProgressiveTextRenderer(
    fullText: String,
    modifier: Modifier = Modifier,
    renderSpeed: RenderSpeed = RenderSpeed.SMOOTH,
    enableMetallic: Boolean = false,
    onComplete: () -> Unit = {},
) {
    UnifiedTextRenderer(
        text = fullText,
        config = TextRenderingConfig(
            renderMode = RenderMode.TYPEWRITER,
            animationIntensity = if (enableMetallic) AnimationIntensity.MEDIUM else AnimationIntensity.NONE,
            renderSpeed = renderSpeed,
            enableMetallic = enableMetallic,
            enableHdr = false,
        ),
        modifier = modifier,
        onRenderComplete = onComplete,
    )
}

// === Preview Section ===

@GymBroPreview
@Composable
private fun UnifiedTextRendererPreview() {
    GymBroTheme {
        androidx.compose.foundation.layout.Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = androidx.compose.foundation.layout.Arrangement.spacedBy(16.dp),
        ) {
            // 瞬显模式
            UnifiedTextRenderer(
                text = "瞬显文本 - PreThink模式",
                config = TextRenderingConfig(
                    renderMode = RenderMode.INSTANT,
                    enableMetallic = true,
                ),
            )

            // 金属跃动标题
            MetallicStreamingText(
                text = "问题分析",
                animationIntensity = AnimationIntensity.HIGH,
            )

            // 渐进式内容
            ProgressiveTextRenderer(
                fullText = "这是一个渐进式显示的长文本内容，模拟AI响应的流式显示效果。",
                renderSpeed = RenderSpeed.FAST,
            )

            // 流式渲染
            UnifiedTextRenderer(
                text = "流式文本渲染效果展示",
                config = TextRenderingConfig(
                    renderMode = RenderMode.STREAMING,
                    enableMetallic = true,
                    animationIntensity = AnimationIntensity.MEDIUM,
                ),
            )
        }
    }
}
