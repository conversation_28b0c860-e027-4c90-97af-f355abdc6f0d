package com.example.gymbro.data.di

import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.data.title.BgeTitleGeneratorImpl
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.title.TitleGenerator
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 🔥 标题生成器依赖注入模块 - 标准Clean Architecture设计
 *
 * 职责：
 * - 绑定domain层TitleGenerator接口到data层实现
 * - 组装所需的依赖（EmbeddingEngine、AiStreamRepository）
 * - 提供单例实例管理
 *
 * 架构原则：
 * - data层模块，可依赖domain接口和core实现
 * - 遵循依赖倒置原则
 * - 支持实现切换（如未来添加其他算法）
 *
 * @since 富文本功能 - Clean Architecture重构
 */
@Module
@InstallIn(SingletonComponent::class)
object TitleGeneratorModule {

    /**
     * 提供TitleGenerator实现
     *
     * 依赖：
     * - EmbeddingEngine：来自core-ml层的算法服务
     * - AiStreamRepository：来自domain层接口，由data层实现
     */
    @Provides
    @Singleton
    fun provideTitleGenerator(
        embeddingEngine: EmbeddingEngine,
        aiStreamRepository: AiStreamRepository,
        conversationIdManager: com.example.gymbro.core.conversation.ConversationIdManager,
    ): TitleGenerator = BgeTitleGeneratorImpl(embeddingEngine, aiStreamRepository, conversationIdManager)
}
