package com.example.gymbro.core.network.service

import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.core.network.processor.StreamingProcessor
import com.example.gymbro.core.network.rest.RestClient
import com.example.gymbro.core.network.rest.ApiResult
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 统一AI响应服务 - Core-Network统一接收点
 *
 * 🎯 新架构职责：
 * - 统一所有AI响应的接收和处理入口
 * - 完整的SSE解析和内容提取（从AiResponseReceiver迁移）
 * - 协议检测和智能路由
 * - 直接输出到各种消费者（ThinkingBox等）
 *
 * 🔥 架构优势：
 * - 单一职责：Core-Network专注于网络响应处理
 * - 模块解耦：Coach模块只负责请求构建
 * - 统一标准：所有AI响应都通过相同的处理管道
 */
@Singleton
class UnifiedAiResponseService @Inject constructor(
    private val restClient: RestClient,
    private val networkConfigManager: NetworkConfigManager,
    private val directOutputChannel: DirectOutputChannel,
    private val streamingProcessor: StreamingProcessor, // 🔥 【利用现有组件】使用现有的SSE解析器
) {

    companion object {
        private val TAG = GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI
    }

    /**
     * 🔥 【新架构核心方法】统一AI流式响应处理
     *
     * 替代原有的 AiResponseReceiver.streamWithNewArchitecture()
     *
     * @param request AI请求对象
     * @param messageId 消息ID
     * @return 处理后的token流
     */
    suspend fun processAiStreamingResponse(
        request: ChatRequest,
        messageId: String
    ): Flow<String> = flow<String> {
        val startTime = System.currentTimeMillis()
        var tokenCount = 0

        Timber.tag(TAG).i("🚀 [请求开始] messageId=$messageId, 开始统一AI响应处理")

        try {
            // 🔥 【数据流验证】记录 Core-Network 接收
            Timber.tag(GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI).d("🔍 [数据流] Core-Network接收: messageId=$messageId")

            // 🔥 【根源修复】使用真正的流式HTTP请求，而不是等待完整响应
            sendStreamingAiRequest(request).collect { rawToken ->
                // 使用现有的StreamingProcessor进行内容提取
                val processedToken = streamingProcessor.processImmediate(
                    token = rawToken,
                    contentType = ContentType.JSON_SSE,
                    messageId = messageId
                )

                if (processedToken.isNotEmpty()) {
                    tokenCount++

                    // 🔥 【Plan B重构】直接使用messageId，消除conversationId概念
                    directOutputChannel.sendToken(
                        token = processedToken,
                        messageId = messageId, // 🔥 【Plan B重构】统一使用messageId参数名
                        contentType = ContentType.JSON_SSE,
                        metadata = mapOf(
                            "source" to "unified-ai-service-streaming",
                            "timestamp" to System.currentTimeMillis()
                        )
                    )

                    // 🔥 【重要】不再emit给调用者，避免Coach重复处理
                    // ThinkingBox通过DirectOutputChannel订阅即可
                }
            }

            val processingTime = System.currentTimeMillis() - startTime

            // 🔥 【新增】更新服务统计
            totalProcessedRequests++
            totalProcessingTimeMs += processingTime

            Timber.tag(TAG).i("✅ [请求完成] messageId=$messageId, tokens=$tokenCount, 耗时=${processingTime}ms")

        } catch (e: Exception) {
            val processingTime = System.currentTimeMillis() - startTime
            Timber.tag(TAG).e(e, "❌ [请求失败] messageId=$messageId, tokens=$tokenCount, 耗时=${processingTime}ms, 错误=${e.message}")

            // 🔥 【Plan B重构】发送错误信息到DirectOutputChannel，使用messageId
            directOutputChannel.sendToken(
                token = "AI响应处理失败: ${e.message}",
                messageId = messageId, // 🔥 【Plan B重构】统一使用messageId参数名
                contentType = ContentType.PLAIN_TEXT,
                metadata = mapOf("error" to true, "source" to "unified-ai-service")
            )
            throw e
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 🔥 【根源修复】真正的流式AI请求 - 实时处理每个SSE事件
     */
    private fun sendStreamingAiRequest(request: ChatRequest): Flow<String> = flow<String> {
        val config = networkConfigManager.getCurrentConfig()
        val url = "${config.restBase}/v1/chat/completions"

        val requestBody = Json.encodeToString(ChatRequest.serializer(), request)
        val headers = mapOf(
            "Content-Type" to "application/json",
            "Authorization" to "Bearer ${config.apiKey}",
            "Accept" to "text/event-stream",
            "Cache-Control" to "no-cache",
            "stream" to "true"  // 🔥 【新增】明确指定流式响应
        )

        Timber.tag(TAG).d("📡 [流式AI请求] 发送到: $url")

        try {
            // 🔥 【真实HTTP流式请求】使用新的真实流式方法
            val sseFlow = restClient.postStreamingFlow(url, requestBody, headers)

            sseFlow.collect { line ->
                Timber.v("📡 [实时SSE行] $line")

                when {
                    line.startsWith("data: ") -> {
                        val dataContent = line.substring(6) // 移除 "data: " 前缀
                        if (dataContent.trim() != "[DONE]") {
                            Timber.v("📡 [SSE数据] $dataContent")
                            emit(line) // 发送完整的 "data: ..." 行
                        } else {
                            Timber.d("🏁 [SSE完成] 检测到 [DONE] 标记")
                            return@collect // 结束流
                        }
                    }
                    line.startsWith("event: ") -> {
                        Timber.v("📋 [SSE事件] $line")
                    }
                    line.startsWith("id: ") -> {
                        Timber.v("🆔 [SSE ID] $line")
                    }
                    line.isEmpty() -> {
                        // SSE 事件分隔符，正常跳过
                    }
                    else -> {
                        Timber.v("📝 [SSE其他] $line")
                    }
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ [流式请求异常] ${e.message}")
            throw e
        }
    }.flowOn(Dispatchers.IO)

    // 🔥 【根源修复】parseSseToTokenFlow方法已移除
    // 现在直接从sendStreamingAiRequest获取实时的SSE行，无需等待完整响应后分割

    /**
     * 🔥 【新增】直接订阅处理结果的便捷方法
     *
     * 供消费者（如ThinkingBox）直接订阅使用
     */
    fun subscribeToProcessedTokens(messageId: String): Flow<String> {
        return directOutputChannel.subscribeToMessage(messageId)
            .map { outputToken -> outputToken.content }
    }

    // 🔥 【新增】服务统计计数器
    @Volatile
    private var totalProcessedRequests = 0L
    @Volatile
    private var totalProcessingTimeMs = 0L
    @Volatile
    private var serviceStartTime = System.currentTimeMillis()

    /**
     * 🔥 【修复】获取服务状态 - 实现真实的统计
     */
    fun getServiceStatus(): ServiceStatus {
        val avgLatency = if (totalProcessedRequests > 0) {
            totalProcessingTimeMs / totalProcessedRequests
        } else {
            0L
        }

        return ServiceStatus(
            isActive = true,
            totalProcessedRequests = totalProcessedRequests,
            averageLatencyMs = avgLatency,
            uptimeMs = System.currentTimeMillis() - serviceStartTime
        )
    }
}

/**
 * 🔥 【修复】服务状态数据类 - 添加更多统计信息
 */
data class ServiceStatus(
    val isActive: Boolean,
    val totalProcessedRequests: Long,
    val averageLatencyMs: Long,
    val uptimeMs: Long = 0L
)
