package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import app.cash.turbine.test
import com.example.gymbro.core.network.DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxReducer
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import kotlin.test.assertEquals
import kotlin.test.assertFalse

/**
 * ThinkingBoxViewModel测试 - Plan B重构版本
 * 
 * 🔥 【Plan B重构】验证ViewModel MVI架构，确保sessionId移除后的功能完整性
 */
class ThinkingBoxViewModelTest {

    private lateinit var viewModel: ThinkingBoxViewModel
    private lateinit var mockThinkingBoxReducer: ThinkingBoxReducer
    private lateinit var mockDomainMapper: DomainMapper
    private lateinit var mockStreamingParser: StreamingThinkingMLParser
    private lateinit var mockDirectOutputChannel: DirectOutputChannel

    @BeforeEach
    fun setup() {
        mockThinkingBoxReducer = mockk()
        mockDomainMapper = mockk()
        mockStreamingParser = mockk()
        mockDirectOutputChannel = mockk()

        viewModel = ThinkingBoxViewModel(
            thinkingBoxReducer = mockThinkingBoxReducer,
            domainMapper = mockDomainMapper,
            streamingParser = mockStreamingParser,
            directOutputChannel = mockDirectOutputChannel
        )
    }

    @Test
    @DisplayName("【初始化】ViewModel初始状态应该正确")
    fun `should have correct initial state`() = runTest {
        // When & Then
        viewModel.state.test {
            val initialState = awaitItem()
            assertEquals("", initialState.messageId)
            // 🔥 【Plan B重构】不再验证sessionId
            assertEquals(emptyList(), initialState.segmentsQueue)
            assertFalse(initialState.finalReady)
            assertEquals("", initialState.finalContent)
            assertFalse(initialState.thinkingClosed)
            assertEquals(null, initialState.error)
            assertFalse(initialState.isLoading)
        }
    }

    @Test
    @DisplayName("【初始化】initialize方法应该正确分发Intent")
    fun `should dispatch Initialize intent correctly`() = runTest {
        // Given
        val messageId = "test-message-123"
        val expectedState = ThinkingBoxContract.State(messageId = messageId)
        
        every { 
            mockThinkingBoxReducer.reduce(any<ThinkingBoxContract.Intent.Initialize>(), any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateOnly(expectedState)

        // When
        viewModel.initialize(messageId)

        // Then
        verify { 
            mockThinkingBoxReducer.reduce(
                ThinkingBoxContract.Intent.Initialize(messageId), 
                any()
            ) 
        }
        
        viewModel.state.test {
            val state = awaitItem()
            assertEquals(messageId, state.messageId)
            // 🔥 【Plan B重构】不再验证sessionId参数
        }
    }

    @Test
    @DisplayName("【重置】reset方法应该正确分发Intent")
    fun `should dispatch Reset intent correctly`() = runTest {
        // Given
        val resetState = ThinkingBoxContract.State()
        
        every { 
            mockThinkingBoxReducer.reduce(ThinkingBoxContract.Intent.Reset, any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateOnly(resetState)

        // When
        viewModel.reset()

        // Then
        verify { 
            mockThinkingBoxReducer.reduce(ThinkingBoxContract.Intent.Reset, any()) 
        }
    }

    @Test
    @DisplayName("【UI回调】onSegmentRendered方法应该正确分发Intent")
    fun `should dispatch UiSegmentRendered intent correctly`() = runTest {
        // Given
        val segmentId = "segment-123"
        val currentState = ThinkingBoxContract.State(
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = segmentId,
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.THINKING,
                    title = "思考",
                    content = "内容",
                    isComplete = true,
                    isRendered = true
                )
            )
        )
        
        every { 
            mockThinkingBoxReducer.reduce(any<ThinkingBoxContract.Intent.UiSegmentRendered>(), any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateOnly(currentState)

        // When
        viewModel.onSegmentRendered(segmentId)

        // Then
        verify { 
            mockThinkingBoxReducer.reduce(
                ThinkingBoxContract.Intent.UiSegmentRendered(segmentId), 
                any()
            ) 
        }
    }

    @Test
    @DisplayName("【错误处理】clearError方法应该正确分发Intent")
    fun `should dispatch ClearError intent correctly`() = runTest {
        // Given
        val clearedState = ThinkingBoxContract.State(error = null)
        
        every { 
            mockThinkingBoxReducer.reduce(ThinkingBoxContract.Intent.ClearError, any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateOnly(clearedState)

        // When
        viewModel.clearError()

        // Then
        verify { 
            mockThinkingBoxReducer.reduce(ThinkingBoxContract.Intent.ClearError, any()) 
        }
    }

    @Test
    @DisplayName("【Token流】StartTokenStreamListening Effect应该正确处理")
    fun `should handle StartTokenStreamListening effect correctly`() = runTest {
        // Given
        val messageId = "test-message-456"
        val tokenFlow = flowOf("token1", "token2", "token3")
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId) 
        } returns tokenFlow
        
        every { 
            mockStreamingParser.parseTokenChunk(any(), any(), any()) 
        } returns Unit
        
        val initializeState = ThinkingBoxContract.State(messageId = messageId)
        val effect = ThinkingBoxContract.Effect.StartTokenStreamListening(messageId)
        
        every { 
            mockThinkingBoxReducer.reduce(any<ThinkingBoxContract.Intent.Initialize>(), any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateAndEffect(initializeState, effect)

        // When
        viewModel.initialize(messageId)

        // Then
        coVerify { mockDirectOutputChannel.subscribeToMessage(messageId) }
        // 注意：由于token流处理是异步的，这里主要验证订阅调用
    }

    @Test
    @DisplayName("【Token流】Token解析应该正确调用Parser")
    fun `should call parser for token processing correctly`() = runTest {
        // Given
        val messageId = "test-message-789"
        val token = "<thinking>测试思考内容</thinking>"
        val tokenFlow = flowOf(token)
        
        coEvery { 
            mockDirectOutputChannel.subscribeToMessage(messageId) 
        } returns tokenFlow
        
        every { 
            mockStreamingParser.parseTokenChunk(token, messageId, any()) 
        } returns Unit
        
        val initializeState = ThinkingBoxContract.State(messageId = messageId)
        val effect = ThinkingBoxContract.Effect.StartTokenStreamListening(messageId)
        
        every { 
            mockThinkingBoxReducer.reduce(any<ThinkingBoxContract.Intent.Initialize>(), any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateAndEffect(initializeState, effect)

        // When
        viewModel.initialize(messageId)
        
        // 等待异步处理
        kotlinx.coroutines.delay(100)

        // Then
        verify { 
            mockStreamingParser.parseTokenChunk(token, messageId, any()) 
        }
    }

    @Test
    @DisplayName("【Effect处理】ScrollToBottom Effect应该正确处理")
    fun `should handle ScrollToBottom effect correctly`() = runTest {
        // Given
        val effect = ThinkingBoxContract.Effect.ScrollToBottom
        val stateWithEffect = ThinkingBoxContract.State(messageId = "test")
        
        every { 
            mockThinkingBoxReducer.reduce(any(), any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateAndEffect(stateWithEffect, effect)

        // When
        viewModel.initialize("test")

        // Then - Effect应该被发射到effects流
        viewModel.effects.test {
            val emittedEffect = awaitItem()
            assertEquals(effect, emittedEffect)
        }
    }

    @Test
    @DisplayName("【Effect处理】NotifyHistoryThinking Effect应该正确处理")
    fun `should handle NotifyHistoryThinking effect correctly`() = runTest {
        // Given
        val effect = ThinkingBoxContract.Effect.NotifyHistoryThinking(
            messageId = "test-message",
            thinkingMarkdown = "思考内容",
            debounceMs = 100L
        )
        val stateWithEffect = ThinkingBoxContract.State(messageId = "test-message")
        
        every { 
            mockThinkingBoxReducer.reduce(any(), any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateAndEffect(stateWithEffect, effect)

        // When
        viewModel.initialize("test-message")

        // Then
        viewModel.effects.test {
            val emittedEffect = awaitItem()
            assertEquals(effect, emittedEffect)
            assertTrue(emittedEffect is ThinkingBoxContract.Effect.NotifyHistoryThinking)
            val historyEffect = emittedEffect as ThinkingBoxContract.Effect.NotifyHistoryThinking
            assertEquals("test-message", historyEffect.messageId)
            assertEquals("思考内容", historyEffect.thinkingMarkdown)
            assertEquals(100L, historyEffect.debounceMs)
            // 🔥 【Plan B重构】History Effect使用ConversationIdManager获取sessionId
        }
    }

    @Test
    @DisplayName("【Effect处理】NotifyHistoryFinal Effect应该正确处理")
    fun `should handle NotifyHistoryFinal effect correctly`() = runTest {
        // Given
        val effect = ThinkingBoxContract.Effect.NotifyHistoryFinal(
            messageId = "test-message",
            finalMarkdown = "最终答案"
        )
        val stateWithEffect = ThinkingBoxContract.State(messageId = "test-message")
        
        every { 
            mockThinkingBoxReducer.reduce(any(), any()) 
        } returns com.example.gymbro.core.arch.mvi.ReduceResult.stateAndEffect(stateWithEffect, effect)

        // When
        viewModel.initialize("test-message")

        // Then
        viewModel.effects.test {
            val emittedEffect = awaitItem()
            assertEquals(effect, emittedEffect)
            assertTrue(emittedEffect is ThinkingBoxContract.Effect.NotifyHistoryFinal)
            val finalEffect = emittedEffect as ThinkingBoxContract.Effect.NotifyHistoryFinal
            assertEquals("test-message", finalEffect.messageId)
            assertEquals("最终答案", finalEffect.finalMarkdown)
            // 🔥 【Plan B重构】History Effect使用ConversationIdManager获取sessionId
        }
    }
}
