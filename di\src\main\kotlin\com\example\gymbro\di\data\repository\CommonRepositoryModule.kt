package com.example.gymbro.di.data.repository

import com.example.gymbro.data.coach.repository.ChatSessionRepositoryImpl
import com.example.gymbro.data.coach.repository.search.ChatSearchRepositoryImpl
import com.example.gymbro.data.repository.settings.SettingsRepositoryImpl
// 🧹 ARCHITECTURE FIX: 添加ThinkingBox HistoryRepository绑定
import com.example.gymbro.data.thinkingbox.repository.HistoryRepositoryImpl
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.profile.repository.settings.SettingsRepository
import com.example.gymbro.domain.shared.search.search.ChatSearchRepository
import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据层通用Repository绑定模块
 *
 * 负责绑定通用功能相关的Repository接口实现，统一管理跨模块共享的Repository
 * 注意：与di.common.CommonRepositoryModule区分，此模块专门处理数据层Repository绑定
 *
 * 绑定的Repository接口：
 * - SettingsRepository: 应用设置管理
 * - AICoachRepository: AI教练服务
 * - ChatRepository: 聊天会话管理
 *
 * 作用域管理：
 * - Repository使用@Singleton作用域，确保数据访问层的一致性和缓存效果
 */
@Module
@InstallIn(SingletonComponent::class)
internal abstract class DataCommonRepositoryModule {
    /**
     * 绑定设置仓库实现
     *
     * 提供应用设置和用户偏好管理功能，包括：
     * - 应用配置管理
     * - 用户偏好设置
     * - 系统设置同步
     * - 设置备份和恢复
     */
    @Binds
    @Singleton
    internal abstract fun bindSettingsRepository(
        impl: SettingsRepositoryImpl,
    ): SettingsRepository

    /**
     * 绑定聊天会话仓库实现
     *
     * 提供聊天会话管理和消息存储功能，包括：
     * - 会话创建和管理
     * - 消息存储和检索
     * - 会话历史记录
     * - 会话搜索功能
     *
     * 注意：修复接口名称不匹配问题
     * - 实现类：ChatSessionRepositoryImpl
     * - 接口：ChatRepository
     */
    @Binds
    @Singleton
    internal abstract fun bindChatRepository(
        impl: ChatSessionRepositoryImpl,
    ): ChatRepository

    /**
     * 绑定聊天搜索仓库实现
     *
     * 提供聊天历史记录的FTS4全文搜索功能，包括：
     * - 消息内容全文搜索
     * - 会话内搜索
     * - 搜索建议功能
     * - 高性能FTS4索引查询
     */
    @Binds
    @Singleton
    internal abstract fun bindChatSearchRepository(
        impl: ChatSearchRepositoryImpl,
    ): ChatSearchRepository

    /**
     * 🧹 ARCHITECTURE FIX: 绑定ThinkingBox历史仓库实现
     *
     * 提供ThinkingBox思考历史记录管理功能，包括：
     * - 思考消息元数据存储
     * - 思考阶段详细内容记录
     * - 最终markdown内容存储
     * - 完整思考历史查询
     *
     * 架构修复：将绑定从Features层移至Data层，遵循Clean Architecture原则
     */
    @Binds
    @Singleton
    internal abstract fun bindHistoryRepository(
        impl: HistoryRepositoryImpl,
    ): HistoryRepository
}
