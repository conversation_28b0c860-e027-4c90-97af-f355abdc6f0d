package com.example.gymbro.features.coach.history.logging

import timber.log.Timber
import com.example.gymbro.core.util.CompactIdGenerator

/**
 * Coach History模块专用日志工具类
 *
 * 🎯 遵循COA-子包-*标签规范
 * - 使用COA-HISTORY-*前缀标识history子模块日志
 * - 统一接入core的timber log动态管理
 * - 支持对话历史记录的完整生命周期跟踪
 *
 * 🔧 使用示例：
 * ```kotlin
 * Timber.tag(HistoryLogUtils.TAG_CORE).i("历史记录加载开始")
 * Timber.tag(HistoryLogUtils.TAG_PAGING).d("分页加载: page=${page}")
 * HistoryLogUtils.logHistoryFlow("LOAD", "加载对话历史", sessionId)
 * ```
 */
object HistoryLogUtils {

    // === 简化COA-HISTORY标签定义（只保留3个核心标签）===

    /** 核心历史记录业务 */
    const val TAG_CORE = "COA-HISTORY"

    /** 错误处理 */
    const val TAG_ERROR = "COA-HISTORY-ERROR"

    /** 调试信息 */
    const val TAG_DEBUG = "COA-HISTORY-DEBUG"

    // === 向后兼容标签 ===
    /** @deprecated 使用 TAG_CORE 替代 */
    const val TAG_HISTORY_ACTOR = "HISTORY-ACTOR"

    // === 简化日志方法 ===

    /**
     * 🔥 历史记录流程跟踪
     */
    fun logHistoryFlow(step: String, description: String, sessionId: String = "") {
        val message = buildString {
            append("🔥 [HISTORY-$step] $description")
            if (sessionId.isNotEmpty()) append(" - sessionId=$sessionId")
        }
        Timber.tag(TAG_CORE).i(message)
    }

    /**
     * 🔥 历史记录错误跟踪
     */
    fun logHistoryError(step: String, description: String, error: String, sessionId: String = "") {
        val message = buildString {
            append("❌ [HISTORY-$step] $description - ERROR: $error")
            if (sessionId.isNotEmpty()) append(" - sessionId=$sessionId")
        }
        Timber.tag(TAG_ERROR).e(message)
    }



    /**
     * 🔥 快速日志方法 - 核心业务
     */
    object Core {
        fun info(message: String) = Timber.tag(TAG_CORE).i(message)
        fun debug(message: String) = Timber.tag(TAG_DEBUG).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_ERROR).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_CORE).w(message)
    }
}
