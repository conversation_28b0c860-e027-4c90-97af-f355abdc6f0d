package com.example.gymbro.features.auth.ui.layout

import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
// 🧹 HARDCODE CLEANUP: 使用设计系统Token替代硬编码值
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * Auth模块布局落位常量
 *
 * 基于精确坐标系统的布局规范：
 * - 屏幕左上角为原点 (0, 0)
 * - 右下角为 (100%, 100%)
 * - 所有元素按照固定坐标位置布局
 */
object AuthLayoutConstants {

    // === 垂直位置坐标 (Y轴) ===
    const val STATUS_BAR_Y_START = 0.0f // 0%
    const val STATUS_BAR_Y_END = 0.04f // 4%

    const val APP_NAME_Y_CENTER = 0.22f // 22% - 应用名称中心点
    const val SUBTITLE_Y_CENTER = 0.30f // 30% - 副标题中心点
    const val DECORATION_Y_CENTER = 0.35f // 35% - 装饰图形中心点
    const val TAGLINE_Y_CENTER = 0.45f // 45% - 标语文本中心点

    const val BUTTON_GROUP_Y_START = 0.55f // 55% - 按钮组开始位置
    const val BUTTON_GROUP_Y_END = 0.85f // 85% - 按钮组结束位置

    const val LEGAL_TEXT_Y_CENTER = 0.95f // 95% - 法律声明中心点

    // === 水平位置坐标 (X轴) ===
    const val CENTER_X = 0.5f // 50% - 水平中心线
    const val CONTENT_MARGIN_X = 0.10f // 10% - 内容边距 (两侧各10%)
    const val CONTENT_WIDTH = 0.80f // 80% - 内容区域宽度

    // === 尺寸规格 ===
    // 字体尺寸
    val APP_NAME_FONT_SIZE = 72.sp
    val SUBTITLE_FONT_SIZE = 18.sp
    val TAGLINE_FONT_SIZE = 18.sp
    val LEGAL_TEXT_FONT_SIZE = 12.sp

    // 装饰元素尺寸 - 🧹 HARDCODE CLEANUP: 使用Token系统
    val DECORATION_SIZE = 120.dp // 保持原有设计，暂时使用硬编码直到有合适的Token
    val DECORATION_SIZE_SMALL = 100.dp // 保持原有设计，暂时使用硬编码直到有合适的Token

    // 按钮尺寸 - 🧹 HARDCODE CLEANUP: 使用Token系统
    val BUTTON_HEIGHT = Tokens.Button.HeightLarge // 64.dp - 使用Token系统
    val BUTTON_CORNER_RADIUS = Tokens.Radius.Button // 8.dp - 使用Token系统

    // === 透明度和颜色 ===
    const val SUBTITLE_ALPHA = 0.7f
    const val TAGLINE_ALPHA = 0.8f
    const val LEGAL_TEXT_ALPHA = 0.6f

    // === 间距规格 === - 🧹 HARDCODE CLEANUP: 使用Token系统
    val VERTICAL_SPACING_SMALL = Tokens.Spacing.Small // 8.dp
    val VERTICAL_SPACING_MEDIUM = Tokens.Spacing.Medium // 16.dp
    val VERTICAL_SPACING_LARGE = Tokens.Spacing.Large // 24.dp
    val VERTICAL_SPACING_XLARGE = Tokens.Spacing.XLarge // 32.dp

    val HORIZONTAL_PADDING = Tokens.Spacing.Large // 24.dp

    // === 动画规格 ===
    const val ANIMATION_ENTER_DELAY_MS = 200
    const val ANIMATION_DURATION_MS = 400

    // === 布局分区计算 ===
    // 根据Y轴坐标计算各区域的高度比例
    fun getHeightRatio(startY: Float, endY: Float): Float {
        return endY - startY
    }

    // 应用名称区域高度比例 (从状态栏结束到应用名称中心)
    val APP_NAME_SECTION_HEIGHT = getHeightRatio(STATUS_BAR_Y_END, APP_NAME_Y_CENTER)

    // 副标题区域高度比例
    val SUBTITLE_SECTION_HEIGHT = getHeightRatio(APP_NAME_Y_CENTER, SUBTITLE_Y_CENTER)

    // 装饰区域高度比例
    val DECORATION_SECTION_HEIGHT = getHeightRatio(SUBTITLE_Y_CENTER, DECORATION_Y_CENTER)

    // 标语区域高度比例
    val TAGLINE_SECTION_HEIGHT = getHeightRatio(DECORATION_Y_CENTER, TAGLINE_Y_CENTER)

    // 按钮组区域高度比例
    val BUTTON_GROUP_SECTION_HEIGHT = getHeightRatio(BUTTON_GROUP_Y_START, BUTTON_GROUP_Y_END)

    // 法律声明区域高度比例 (从按钮组结束到法律声明中心)
    val LEGAL_SECTION_HEIGHT = getHeightRatio(BUTTON_GROUP_Y_END, LEGAL_TEXT_Y_CENTER)
}

/**
 * 扩展函数：简化布局坐标使用
 */
object AuthLayoutHelpers {

    /**
     * 创建垂直居中的Box布局修饰符
     */
    fun getVerticalCenterModifier(heightRatio: Float): Modifier {
        return Modifier
            .fillMaxWidth()
            .fillMaxHeight(heightRatio)
    }

    /**
     * 获取内容区域的水平padding
     */
    fun getContentPadding() = AuthLayoutConstants.HORIZONTAL_PADDING

    /**
     * 获取按钮组的垂直间距
     */
    fun getButtonSpacing() = AuthLayoutConstants.VERTICAL_SPACING_MEDIUM
}
