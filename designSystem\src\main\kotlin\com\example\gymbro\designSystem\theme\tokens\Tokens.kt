package com.example.gymbro.designSystem.theme.tokens

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.motion.MotionEasings
import com.example.gymbro.designSystem.theme.motion.MotionSpecs
import timber.log.Timber

/**
 * GymBro设计系统令牌 v2.0
 * 整合13级精致灰阶系统和高级品牌色，提供更丰富的视觉层次
 */

// === 颜色Token系统 v2.0 ===
object ColorTokens {
    // === 13级精致灰阶系统 ===
    // 提供更丰富的灰度层次，让深浅主题都更加精致
    val Gray000 = GrayScale.Gray000 // 纯黑 - 最深背景
    val Gray050 = GrayScale.Gray050 // 深空黑 - 深层界面背景
    val Gray100 = GrayScale.Gray100 // 深炭黑 - 卡片背景（深色主题）
    val Gray200 = GrayScale.Gray200 // 炭灰 - 次级背景/分割线
    val Gray300 = GrayScale.Gray300 // 深石墨 - 禁用状态/边框
    val Gray400 = GrayScale.Gray400 // 石墨 - 次要文本（深色主题）
    val Gray500 = GrayScale.Gray500 // 中性灰 - 占位符文本
    val Gray600 = GrayScale.Gray600 // 暖灰 - 标签文本
    val Gray700 = GrayScale.Gray700 // 浅石墨 - 主要文本（浅色主题）
    val Gray800 = GrayScale.Gray800 // 银灰 - 次要文本（浅色主题）
    val Gray850 = GrayScale.Gray850 // 淡银 - 分割线（浅色主题）
    val Gray900 = GrayScale.Gray900 // 珍珠白 - 背景变体（浅色主题）
    val Gray950 = GrayScale.Gray950 // 纯白 - 主背景（浅色主题）

    // === 高级品牌色系统 ===
    // 采用更高级的铂金银色调，提升品牌质感
    val BrandPrimary = BrandColors.PlatinumSilver // 主品牌色 - 铂金银
    val BrandSecondary = BrandColors.DeepSilver // 深银色 - 用于强调
    val BrandTertiary = BrandColors.LightSilver // 浅银色 - 用于次要元素

    // CTA 颜色系统 - 优雅橙色系列
    val CTAPrimary = BrandColors.PremiumOrange // 主CTA色 - 优雅橙
    val CTAHover = BrandColors.SoftOrange // 悬停状态 - 柔和橙
    val CTAPressed = BrandColors.DeepOrange // 按压状态 - 深橙

    // === 功能性颜色系统 ===
    // 成功色系 - 优雅绿色
    val Success = FunctionalColors.Success
    val SuccessLight = FunctionalColors.SuccessLight
    val SuccessDark = FunctionalColors.SuccessDark

    // 警告色系 - 琥珀色
    val Warning = FunctionalColors.Warning
    val WarningLight = FunctionalColors.WarningLight
    val WarningDark = FunctionalColors.WarningDark

    // 错误色系 - 优雅红色
    val Error = FunctionalColors.Error
    val ErrorLight = FunctionalColors.ErrorLight
    val ErrorDark = FunctionalColors.ErrorDark

    // 信息色系 - 冷蓝色
    val Info = FunctionalColors.Info
    val InfoLight = FunctionalColors.InfoLight
    val InfoDark = FunctionalColors.InfoDark

    // === Progress tracking colors ===
    object Progress {
        // Progress states - optimized for both light and dark themes
        val Idle = Gray400 // Neutral gray for idle state
        val Ongoing = BrandPrimary // Active progress uses brand color
        val Done = Success // Completed state uses success color

        // Light theme specific
        object Light {
            val Idle = Gray700 // Darker gray for better contrast on light background
            val Ongoing = BrandSecondary // Deep silver for light theme
            val Done = SuccessDark // Darker success color for light theme
        }

        // Dark theme specific
        object Dark {
            val Idle = Gray400 // Lighter gray for dark background
            val Ongoing = BrandPrimary // Platinum silver
            val Done = Success // Standard success color
        }
    }

    // === 语义化颜色映射 - 深色主题 ===
    object Dark {
        // 主要界面颜色
        val Background = Gray000 // 纯黑背景 - 高端感
        val OnBackground = Gray800 // 背景上的文本 - 银灰色
        val BackgroundSubtle = Gray050 // 微妙背景 - 让卡片浮起的底色
        val Surface = Gray100 // 表面颜色 - 深炭黑
        val OnSurface = Gray800 // 表面上的文本
        val SurfaceVariant = Gray200 // 表面变体 - 炭灰
        val SurfaceVariantTransparent = Gray200.copy(alpha = 0.08f) // 8%透明度表面变体
        val OnSurfaceVariant = Gray700 // 表面变体上的文本

        // 品牌颜色应用
        val Primary = BrandPrimary // 主色 - 铂金银
        val OnPrimary = Gray000 // 主色上的文本
        val PrimaryContainer = Gray200 // 主色容器
        val OnPrimaryContainer = Gray800 // 主色容器上的文本

        val Secondary = Gray600 // 次要色 - 暖灰
        val OnSecondary = Gray950 // 次要色上的文本
        val SecondaryContainer = Gray200 // 次要色容器
        val OnSecondaryContainer = Gray800 // 次要色容器上的文本

        // 轮廓和分割线
        val Outline = Gray400 // 主要轮廓 - 石墨色
        val OutlineVariant = Gray300 // 次要轮廓 - 深石墨

        // 特殊用途
        val Scrim = Color(0x99000000) // 遮罩 - 60%黑色
        val SurfaceTint = BrandPrimary // 表面着色
    }

    // === 语义化颜色映射 - 浅色主题 ===
    object Light {
        // 主要界面颜色
        val Background = Gray950 // 纯白背景 - 干净优雅
        val OnBackground = Gray300 // 背景上的文本 - 深石墨
        val BackgroundSubtle = Gray900 // 微妙背景 - 让卡片浮起的底色
        val Surface = Gray950 // 表面颜色 - 纯白
        val OnSurface = Gray300 // 表面上的文本
        val SurfaceVariant = Gray900 // 表面变体 - 珍珠白
        val SurfaceVariantTransparent = Gray900.copy(alpha = 0.08f) // 8%透明度表面变体
        val OnSurfaceVariant = Gray500 // 表面变体上的文本

        // 品牌颜色应用
        val Primary = BrandSecondary // 主色 - 深银色（更适合浅色主题）
        val OnPrimary = Gray950 // 主色上的文本
        val PrimaryContainer = Gray900 // 主色容器
        val OnPrimaryContainer = Gray300 // 主色容器上的文本

        val Secondary = Gray600 // 次要色 - 暖灰
        val OnSecondary = Gray950 // 次要色上的文本
        val SecondaryContainer = Gray850 // 次要色容器
        val OnSecondaryContainer = Gray300 // 次要色容器上的文本

        // 轮廓和分割线
        val Outline = Gray700 // 主要轮廓 - 浅石墨
        val OutlineVariant = Gray850 // 次要轮廓 - 淡银

        // 特殊用途
        val Scrim = Color(0x33000000) // 遮罩 - 20%黑色
        val SurfaceTint = BrandSecondary // 表面着色
    }

    // === 组件专用颜色 ===
    object Component {
        // 按钮颜色
        val ButtonPrimary = CTAPrimary // 主要按钮
        val ButtonSecondary = Gray600 // 次要按钮
        val ButtonDisabled = Gray400 // 禁用按钮

        // 输入框颜色
        val InputBackground = Gray050 // 输入框背景（深色主题）
        val InputBackgroundLight = Gray900 // 输入框背景（浅色主题）
        val InputBorder = Gray400 // 输入框边框
        val InputBorderFocused = BrandPrimary // 输入框聚焦边框

        // 分割线颜色
        val DividerPrimary = Gray300 // 主要分割线（深色主题）
        val DividerPrimaryLight = Gray850 // 主要分割线（浅色主题）
        val DividerSecondary = Gray200 // 次要分割线（深色主题）
        val DividerSecondaryLight = Gray900 // 次要分割线（浅色主题）
    }

    // === 模块专属颜色系统 ===
    object ProfileColors {
        val avatarBackground = Gray100 // 头像背景
        val avatarBorder = Gray300 // 头像边框
        val settingItemBackground = Gray050 // 设置项背景
        val settingItemBorder = Gray200 // 设置项边框
        val badgeBackground = BrandPrimary // 徽章背景
        val badgeText = Gray000 // 徽章文本
    }

    object WorkoutColors {
        // 基础卡片颜色 - 支持动态主题
        val cardBackground = Gray100 // 卡片背景
        val cardBorder = Gray300 // 卡片边框
        val cardElevation = Gray050 // 卡片阴影

        // 文本颜色系统
        val textPrimary = Gray800 // 主要文本色
        val textSecondary = Gray600 // 次要文本色
        val textTertiary = Gray500 // 第三级文本色
        val textDisabled = Gray400 // 禁用文本色

        // AI Coach集成专用颜色
        val aiCoachPrimary = BrandPrimary // AI教练主色
        val aiCoachBackground = Gray100 // AI教练背景
        val aiCoachText = Gray800 // AI教练文本

        // 训练状态颜色
        val activeState = Success // 活动状态
        val completedState = Info // 完成状态
        val restState = Warning // 休息状态
        val timerBackground = Gray200 // 计时器背景

        // 状态指示颜色
        val successPrimary = Success // 成功状态主色
        val errorPrimary = Error // 错误状态主色
        val warningPrimary = Warning // 警告状态主色
        val infoPrimary = Info // 信息状态主色

        // 动态强调色
        val accentPrimary = BrandPrimary // 主要强调色
        val accentSecondary = Gray600 // 次要强调色

        // 主题过渡辅助色
        val transitionOverlay = Gray000 // 主题切换遮罩
    }

    // 🧹 REMOVED: 废弃的向后兼容Token已清理
    // 所有Gray00-Gray95、BrandSilver和BrandOrange已迁移到新的命名系统
}

// === 间距Token系统（保持不变，已经很好） ===
object SpacingTokens {
    val None = 0.dp
    val Tiny = 2.dp // 微小间距
    val XSmall = 4.dp // 超小间距
    val Small = 8.dp // 小间距
    val Medium = 16.dp // 中等间距（基准）
    val Large = 24.dp // 大间距
    val XLarge = 32.dp // 超大间距
    val XXLarge = 48.dp // 特大间距
    val Huge = 64.dp // 巨大间距
    val Massive = 96.dp // 超巨大间距

    // === 语义化间距 ===
    val ComponentPadding = Medium // 组件内边距
    val SectionSpacing = Large // 区块间距
    val PageMargin = Medium // 页面边距
    val ListItemSpacing = Small // 列表项间距
    val ButtonPadding = Medium // 按钮内边距
    val CardPadding = Medium // 卡片内边距
}

// === 圆角Token系统（优化，添加更多层次） ===
object RadiusTokens {
    val None = 0.dp
    val Tiny = 2.dp // 超小圆角 - 用于细节元素
    val XSmall = 4.dp // 小元素圆角 - 标签、徽章
    val Small = 8.dp // 基础圆角 - 按钮、输入框
    val Medium = 12.dp // 卡片圆角 - 普通卡片
    val Large = 16.dp // 大卡片圆角 - 重要卡片
    val XLarge = 24.dp // 特殊圆角 - 模态框、底部表单
    val XXLarge = 32.dp // 超大圆角 - 特殊设计元素
    val Round = 100.dp // 完全圆形 - 头像、FAB

    // === 语义化圆角 ===
    val Button = Small // 按钮圆角
    val ButtonLarge = Medium // 大按钮圆角
    val Card = XLarge // 卡片圆角 - 美化方案：统一使用24dp
    val CardLarge = Large // 大卡片圆角
    val Modal = Large // 模态框圆角
    val BottomSheet = XLarge // 底部表单圆角
    val Avatar = Round // 头像圆角
    val Input = Small // 输入框圆角
    val Chip = XSmall // 标签圆角
}

// === 字体Token系统（对齐UI布局总方案） ===
object TypographyTokens {
    // 基础字体大小 - 遵循Material Design 3
    val Tiny = 10.sp // 超小文字 - 标签、说明
    val Small = 12.sp // 小文字 - 辅助文本
    val Body = 14.sp // 正文 - 标准阅读
    val BodyMedium = 16.sp // 中等正文 - 重要内容
    val BodyLarge = 18.sp // 大正文 - 标题性内容
    val Headline = 20.sp // 标题 - 区块标题
    val HeadlineLarge = 24.sp // 大标题 - 页面标题
    val Display = 28.sp // 展示文字 - 重要标题
    val DisplayLarge = 32.sp // 超大展示 - 特殊标题

    // === 语义化字体大小（对应UI方案中的text_token） ===
    val TextTokenHeadingLarge = DisplayLarge // text_token_heading_large: 32sp
    val TextTokenBodyMedium = BodyMedium // text_token_body_medium: 16sp
    val TextTokenLabelSmall = Small // text_token_label_small: 12sp

    // === 专用场景字体 ===
    val ButtonText = BodyMedium // 按钮文字
    val InputText = BodyMedium // 输入框文字
    val InputPlaceholder = Body // 输入框占位符
    val InputHint = Small // 输入框提示
    val TooltipText = Small // 提示框文字
    val CaptionText = Tiny // 说明文字
    val TabText = Body // 标签页文字
    val AppBarTitle = HeadlineLarge // 应用栏标题
    val CardTitle = Headline // 卡片标题
    val CardSubtitle = Body // 卡片副标题
    val ListItemTitle = BodyMedium // 列表项标题
    val ListItemSubtitle = Body // 列表项副标题
}

// === Size Token系统 ===
object SizeTokens {
    // Dot sizes for progress indicators and status
    val DotTiny = 4.dp // Tiny dot for minimal indicators
    val DotSmall = 8.dp // Small dot for progress tracking
    val DotMedium = 12.dp // Medium dot for standard indicators
    val DotLarge = 16.dp // Large dot for prominent indicators

    // Badge sizes
    val BadgeSmall = 16.dp // Small badge size
    val BadgeMedium = 20.dp // Medium badge size
    val BadgeLarge = 24.dp // Large badge size

    // Indicator sizes
    val IndicatorSmall = 2.dp // Small indicator height/width
    val IndicatorMedium = 4.dp // Medium indicator height/width
    val IndicatorLarge = 6.dp // Large indicator height/width
}

// === 图标Token系统（整合现有ComponentSizes） ===
object IconTokens {
    // 基础图标大小 - 统一管理所有图标尺寸
    val Tiny = 12.dp // 超小图标 - 状态指示
    val XSmall = 14.dp // 极小图标 - 紧凑布局
    val Small = 16.dp // 小图标 - 辅助功能
    val Medium = 20.dp // 中等图标 - 常用功能
    val Standard = 24.dp // 标准图标 - 主要功能
    val Large = 32.dp // 大图标 - 重要功能
    val XLarge = 40.dp // 超大图标 - 特殊用途
    val XXLarge = 48.dp // 特大图标 - 展示用途

    // === 语义化图标大小（对应UI方案中的icon_token） ===
    val IconTokenSmall = Small // icon_token_small: 16dp
    val IconTokenMedium = Standard // icon_token_medium: 24dp
    val IconTokenLarge = Large // icon_token_large: 32dp

    // === 专用场景图标 ===
    val ButtonIcon = Standard // 按钮图标
    val InputIcon = Standard // 输入框图标
    val NavigationIcon = Standard // 导航图标
    val ActionIcon = Standard // 动作图标
    val TabIcon = Standard // 标签页图标
    val AppBarIcon = Standard // 应用栏图标
    val CardIcon = Medium // 卡片图标
    val ListItemIcon = Standard // 列表项图标
    val FloatingButtonIcon = Standard // 浮动按钮图标
    val TooltipIcon = Small // 提示框图标
    val StatusIcon = Tiny // 状态图标
    val AvatarIcon = XLarge // 头像图标
    val LogoIcon = XXLarge // Logo图标

    // === 专门的触摸目标大小（符合无障碍标准） ===
    val TouchTarget = 48.dp // 最小触摸目标尺寸
    val TouchTargetSmall = 40.dp // 紧凑布局触摸目标
}

// === 按钮Token系统（新增 - 解决硬编码问题） ===
object ButtonTokens {
    // 按钮高度系统 - 基于重要性层级
    val HeightLarge = 64.dp // 主要CTA按钮 - 最重要的操作
    val HeightPrimary = 56.dp // 标准主要按钮 - 常规重要操作
    val HeightSecondary = 48.dp // 次要按钮 - 辅助操作
    val HeightSmall = 40.dp // 紧凑按钮 - 空间受限场景
    val HeightCompact = 32.dp // 超紧凑按钮 - 工具栏等

    // 按钮内边距
    val PaddingHorizontal = SpacingTokens.Medium // 16.dp
    val PaddingVertical = SpacingTokens.Small // 8.dp
    val PaddingCompact = SpacingTokens.XSmall // 4.dp

    // 按钮圆角 - 语义化引用
    val CornerRadius = RadiusTokens.Button // 8.dp

    // 按钮阴影 - 语义化引用
    val Elevation = ElevationTokens.Button // 2.dp
    val ElevationPressed = ElevationTokens.ButtonPressed // 0.dp

    // 按钮最小宽度（无障碍要求）
    val MinWidth = 64.dp // 符合 Material Design 指南
    val MinWidthCompact = 48.dp // 紧凑布局最小宽度

    // 加载指示器尺寸
    val LoadingIndicatorSize = 20.dp // 加载圆圈大小
    val LoadingIndicatorStroke = 2.dp // 加载圆圈线条宽度
}

// === 输入框Token系统（整合ComponentSizes） ===
object InputTokens {
    // 输入框高度系统
    val HeightStandard = 56.dp // 标准输入框高度
    val HeightSmall = 40.dp // 小输入框高度
    val HeightLarge = 64.dp // 大输入框高度

    // 输入框内边距
    val PaddingHorizontal = SpacingTokens.Medium // 16.dp
    val PaddingVertical = SpacingTokens.Small // 8.dp

    // 输入框圆角
    val CornerRadius = RadiusTokens.Input // 8.dp

    // 输入框边框
    val BorderWidth = 1.dp
    val BorderWidthFocused = 2.dp
}

// === 卡片Token系统（整合ComponentSizes） ===
object CardTokens {
    // 卡片高度系统
    val HeightMin = 80.dp // 卡片最小高度
    val HeightSmall = 120.dp // 小卡片高度
    val HeightMedium = 160.dp // 中等卡片高度
    val HeightLarge = 200.dp // 大卡片高度

    // 卡片内边距
    val Padding = SpacingTokens.CardPadding // 16.dp
    val PaddingCompact = SpacingTokens.Small // 8.dp

    // 卡片圆角
    val CornerRadius = RadiusTokens.Card // 24.dp

    // 卡片阴影
    val Elevation = ElevationTokens.Card // 4.dp
}

// === 列表项Token系统（整合ComponentSizes） ===
object ListItemTokens {
    // 列表项高度系统
    val HeightStandard = 72.dp // 标准列表项高度
    val HeightSmall = 56.dp // 小列表项高度
    val HeightLarge = 88.dp // 大列表项高度
    val HeightExpanded = 104.dp // 展开列表项高度

    // 列表项内边距
    val PaddingHorizontal = SpacingTokens.Medium // 16.dp
    val PaddingVertical = SpacingTokens.Small // 8.dp
    val PaddingCompact = SpacingTokens.XSmall // 4.dp

    // 列表项间距
    val Spacing = SpacingTokens.ListItemSpacing // 8.dp
}

// === 应用栏Token系统（整合ComponentSizes） ===
object AppBarTokens {
    // 应用栏高度系统
    val TopBarHeight = 64.dp // 顶部应用栏高度
    val TopBarHeightCompact = 48.dp // 紧凑顶部应用栏高度
    val BottomBarHeight = 80.dp // 底部导航栏高度
    val TabBarHeight = 48.dp // 标签栏高度

    // 应用栏内边距
    val PaddingHorizontal = SpacingTokens.Medium // 16.dp
    val PaddingVertical = SpacingTokens.Small // 8.dp

    // 应用栏阴影
    val Elevation = ElevationTokens.AppBar // 4.dp
}

// === 阴影Token系统（优化，添加更精细的层次） ===
object ElevationTokens {
    val None = 0.dp
    val Subtle = 1.dp // 微妙阴影 - 分割效果
    val XSmall = 2.dp // 超小阴影 - 悬浮按钮
    val Small = 4.dp // 小阴影 - 卡片
    val Medium = 8.dp // 中等阴影 - 导航栏
    val Large = 16.dp // 大阴影 - 模态框
    val XLarge = 24.dp // 特大阴影 - 抽屉导航
    val XXLarge = 32.dp // 超大阴影 - 全屏模态

    // === 语义化阴影 ===
    val Button = XSmall // 按钮阴影
    val ButtonPressed = None // 按钮按下时无阴影
    val Card = Small // 卡片阴影
    val CardHovered = Medium // 卡片悬停阴影
    val Modal = Large // 模态框阴影
    val Tooltip = Medium // 提示框阴影
    val BottomSheet = Large // 底部表单阴影
    val AppBar = Small // 应用栏阴影
    val FloatingButton = Medium // 浮动按钮阴影
}

// === 详细阴影Token系统（对应UI方案中的shadow_token） ===
object ShadowTokens {
    // === 阴影颜色配置 ===
    val AmbientShadowColor = Color.Black.copy(alpha = 0.12f) // 环境阴影颜色
    val SpotShadowColor = Color.Black.copy(alpha = 0.16f) // 聚光阴影颜色
    val ShadowColor = Color.Black.copy(alpha = 0.25f) // 通用阴影颜色
    val ShadowColorLight = Color.Black.copy(alpha = 0.08f) // 浅色主题阴影
    val ShadowColorDark = Color.Black.copy(alpha = 0.40f) // 深色主题阴影

    // === 阴影偏移配置（对应shadow_token_offset） ===
    val OffsetNone = Offset(0f, 0f) // 无偏移
    val OffsetSmall = Offset(0f, 1f) // 小偏移 - shadow_token_offset_small
    val OffsetMedium = Offset(0f, 2f) // 中等偏移
    val OffsetLarge = Offset(0f, 4f) // 大偏移
    val OffsetXLarge = Offset(0f, 8f) // 超大偏移

    // === 阴影模糊半径配置（对应shadow_token_blur） ===
    val BlurNone = 0.dp // 无模糊
    val BlurSmall = 2.dp // 小模糊
    val BlurMedium = 4.dp // 中等模糊 - shadow_token_medium
    val BlurLarge = 8.dp // 大模糊
    val BlurXLarge = 16.dp // 超大模糊

    // === 阴影扩散配置（对应shadow_token_spread） ===
    val SpreadNone = 0.dp // 无扩散 - shadow_token_spread_none
    val SpreadSmall = 1.dp // 小扩散
    val SpreadMedium = 2.dp // 中等扩散
    val SpreadLarge = 4.dp // 大扩散

    // === 自然阴影预设（对应UI方案中的natural_drop_shadow） ===
    object NaturalDropShadow {
        val elevation = ElevationTokens.Medium // 8dp elevation
        val ambientColor = AmbientShadowColor // color_token_shadow_ambient: alpha 0.12
        val spotColor = SpotShadowColor // color_token_shadow_spot: alpha 0.16
        val ambientBlur = BlurLarge // 8dp blur for ambient
        val spotBlur = BlurSmall // 4dp blur for spot
        val spotOffset = OffsetMedium // 2dp offset for spot
    }

    // === 组件专用阴影预设 ===
    object InputShadow {
        val elevation = ElevationTokens.Small // 输入框阴影高度
        val color = ShadowColor // 输入框阴影颜色
        val blur = BlurMedium // 输入框阴影模糊
        val offset = OffsetSmall // 输入框阴影偏移
        val spread = SpreadNone // 输入框阴影扩散
    }

    object CardShadow {
        val elevation = ElevationTokens.Small // 卡片阴影高度
        val color = ShadowColor // 卡片阴影颜色
        val blur = BlurMedium // 卡片阴影模糊
        val offset = OffsetMedium // 卡片阴影偏移
        val spread = SpreadNone // 卡片阴影扩散
    }

    object ModalShadow {
        val elevation = ElevationTokens.Large // 模态框阴影高度
        val color = ShadowColorDark // 模态框阴影颜色（更深）
        val blur = BlurLarge // 模态框阴影模糊
        val offset = OffsetLarge // 模态框阴影偏移
        val spread = SpreadSmall // 模态框阴影扩散
    }
}

// === Motion Token系统 ===
object MotionTokens {
    // Motion duration tokens
    object Duration {
        const val Short = 400 // 400ms for short animations
    }
}

/**
 * 统一的设计令牌访问点 v2.0
 * 整合所有Token系统的统一入口
 *
 * 现在包含完整的组件Token系统，替代了传统的ComponentSizes
 */
object Tokens {
    // 🧹 MIGRATED: AnimTokens已迁移到新的Motion系统
    val Motion = MotionTokens // Motion Token系统 (新增)
    val Color = ColorTokens
    val Spacing = SpacingTokens
    val Radius = RadiusTokens
    val Elevation = ElevationTokens
    val Typography = TypographyTokens
    val Size = SizeTokens // Size Token系统 (新增)
    val Icon = IconTokens
    val Button = ButtonTokens // 按钮Token系统
    val Input = InputTokens // 输入框Token系统 (新增)
    val Card = CardTokens // 卡片Token系统 (新增)
    val ListItem = ListItemTokens // 列表项Token系统 (新增)
    val AppBar = AppBarTokens // 应用栏Token系统 (新增)
    val Shadow = ShadowTokens
}

/**
 * Token使用规范检查器 v2.0
 * 增强的验证系统，检查颜色和尺寸的使用规范
 */
object GymBroTokenValidator {
    // 禁用的硬编码颜色值
    val FORBIDDEN_HARDCODED_COLORS =
        setOf(
            "#000000",
            "#FFFFFF",
            "#C0C0C0",
            "#808080",
            "Color(0xFF000000)",
            "Color(0xFFFFFFFF)",
            "Color.Black",
            "Color.White",
            "Color.Gray",
        )

    // 禁用的硬编码尺寸值
    val FORBIDDEN_HARDCODED_SIZES =
        setOf(
            "8.dp",
            "16.dp",
            "24.dp",
            "32.dp", // 应使用Spacing Token
            "4.dp",
            "12.dp",
            "16.dp", // 应使用Radius Token
            "2.dp",
            "8.dp",
            "16.dp", // 应使用Elevation Token
        )

    // 禁用的硬编码动画值
    val FORBIDDEN_HARDCODED_ANIMATIONS =
        setOf(
            "240",
            "400",
            "1000", // 应使用Animation Token
            "tween(300)",
            "spring(0.8f)", // 应使用MotionSpecs
        )

    /**
     * 检查组件是否使用了硬编码值
     * 仅在DEBUG模式下启用
     *
     * @param componentName 组件名称，用于日志记录
     * @param stackTrace 调用栈，用于检查硬编码使用
     */
    fun validateTokenUsage(
        componentName: String,
        stackTrace: Array<StackTraceElement> = Thread.currentThread().stackTrace,
    ) {
        if (!isDebugMode()) return

        try {
            // 检查调用栈中是否有硬编码值的使用模式
            val violations = mutableListOf<String>()

            stackTrace.forEach { element ->
                val className = element.className
                element.methodName

                // 检查是否在组件文件中使用了硬编码值
                if (className.contains("components") && !className.contains("tokens")) {
                    // 这里可以扩展更复杂的检查逻辑
                    // 例如：检查源代码文件中的硬编码模式
                }
            }

            if (violations.isNotEmpty()) {
                logTokenViolations(componentName, violations)
            } else {
                logTokenCompliance(componentName)
            }
        } catch (e: Exception) {
            // 验证器不应影响应用正常运行
            logValidationError(componentName, e)
        }
    }

    /**
     * 检查是否为 DEBUG 模式
     */
    private fun isDebugMode(): Boolean =
        try {
            // 使用反射检查 BuildConfig.DEBUG
            val buildConfigClass = Class.forName("com.example.gymbro.BuildConfig")
            val debugField = buildConfigClass.getField("DEBUG")
            debugField.getBoolean(null)
        } catch (e: Exception) {
            // 如果无法获取 BuildConfig，默认为 false
            false
        }

    /**
     * 记录 Token 违规
     */
    private fun logTokenViolations(
        componentName: String,
        violations: List<String>,
    ) {
        // 🔥 【日志优化】使用Timber替代println，使用DESIGN-SYSTEM标签
        Timber.tag("DESIGN-SYSTEM").w("🚨 [TokenValidator] $componentName 发现硬编码使用:")
        violations.forEach { violation ->
            Timber.tag("DESIGN-SYSTEM").w("   ❌ $violation")
        }
        Timber.tag("DESIGN-SYSTEM").w("   💡 建议使用 Tokens.* 系统替代硬编码值")
    }

    /**
     * 记录 Token 合规
     */
    private fun logTokenCompliance(componentName: String) {
        // 🔥 【日志优化】使用Timber替代println，使用DESIGN-SYSTEM标签
        Timber.tag("DESIGN-SYSTEM").d("✅ [TokenValidator] $componentName 符合 Token 使用规范")
    }

    /**
     * 记录验证错误
     */
    private fun logValidationError(
        componentName: String,
        error: Exception,
    ) {
        println("⚠️ [TokenValidator] $componentName 验证时发生错误: ${error.message}")
    }

    /**
     * 验证颜色对比度是否符合 WCAG 标准
     */
    fun validateColorContrast(
        foreground: Color,
        background: Color,
    ): Boolean {
        // 简化的对比度检查逻辑
        // 实际实现应该使用 WCAG 2.1 对比度算法
        return true // 占位实现
    }
}
