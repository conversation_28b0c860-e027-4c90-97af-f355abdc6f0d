package com.example.gymbro.features.home.presentation.components

// 🧹 HARDCODE CLEANUP: 使用设计系统Token替代硬编码值
import com.example.gymbro.designSystem.theme.tokens.Tokens
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MyLocation
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.People
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 地图交互栏组件
 * 悬浮在地图上方，包含排行榜、搜索、主题切换、核心按钮等功能
 */
@Composable
fun MapInteractionBar(
    modifier: Modifier = Modifier,
    rankingData: List<com.example.gymbro.features.home.presentation.viewmodel.RankingItem> = emptyList(),
    onSearchClick: () -> Unit = {},
    onThemeToggle: () -> Unit = {},
    onFindPartner: () -> Unit = {},
    onGetLocation: () -> Unit = {},
    onRankingClick: () -> Unit = {},
) {
    Box(modifier = modifier.fillMaxSize()) {
        // 排行榜区域（顶部）
        RankingSection(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium)
                .align(Alignment.TopCenter),
            rankingData = rankingData,
            onRankingClick = onRankingClick,
        )

        // 搜索和控制区域（底部）
        SearchAndControlSection(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium)
                .align(Alignment.BottomCenter),
            onSearchClick = onSearchClick,
            onThemeToggle = onThemeToggle,
            onFindPartner = onFindPartner,
            onGetLocation = onGetLocation,
        )
    }
}

/**
 * 排行榜区域
 */
@Composable
private fun RankingSection(
    modifier: Modifier = Modifier,
    rankingData: List<com.example.gymbro.features.home.presentation.viewmodel.RankingItem> = emptyList(),
    onRankingClick: () -> Unit = {},
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(Tokens.Radius.Medium),
        colors =
        CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Card),
    ) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(Tokens.Spacing.Large),
                )

                Text(
                    text = "附近健身达人排行",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )

                Text(
                    text = "查看更多",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.clickable { onRankingClick() },
                )
            }

            Spacer(modifier = Modifier.height(Tokens.Spacing.Small))

            // 排行榜条目
            if (rankingData.isNotEmpty()) {
                rankingData.take(3).forEach { item ->
                    RankingItem(
                        rank = item.rank,
                        name = item.name,
                        level = item.level,
                        score = item.score,
                    )
                }
            } else {
                // 默认显示占位数据
                RankingItem(
                    rank = 1,
                    name = "张三",
                    level = "Lv4",
                    score = "2580分",
                )

                RankingItem(
                    rank = 2,
                    name = "李四",
                    level = "Lv3",
                    score = "2340分",
                )

                RankingItem(
                    rank = 3,
                    name = "王五",
                    level = "Lv3",
                    score = "2180分",
                )
            }
        }
    }
}

/**
 * 排行榜条目
 */
@Composable
private fun RankingItem(
    rank: Int,
    name: String,
    level: String,
    score: String,
) {
    Row(
        modifier =
        Modifier
            .fillMaxWidth()
            .padding(vertical = Tokens.Spacing.Tiny),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            Text(
                text = "$rank.",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary,
            )

            Surface(
                modifier = Modifier.size(Tokens.Spacing.Medium),
                shape = CircleShape,
                // 🧹 HARDCODE CLEANUP: 使用主题颜色替代硬编码排名颜色
                color =
                when (rank) {
                    1 -> Tokens.Color.Warning // 金色 - 使用语义化颜色
                    2 -> Tokens.Color.Gray600 // 银色 - 使用中性色
                    3 -> Tokens.Color.Info // 铜色 - 使用信息色
                    else -> MaterialTheme.colorScheme.surfaceVariant
                },
            ) {}

            Text(
                text = name,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface,
            )

            Text(
                text = level,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.Bold,
            )
        }

        Text(
            text = score,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
    }
}

/**
 * 搜索和控制区域
 */
@Composable
private fun SearchAndControlSection(
    modifier: Modifier = Modifier,
    onSearchClick: () -> Unit = {},
    onThemeToggle: () -> Unit = {},
    onFindPartner: () -> Unit = {},
    onGetLocation: () -> Unit = {},
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(Tokens.Radius.Large),
        colors =
        CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Card),
    ) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 搜索栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                IconButton(
                    onClick = onSearchClick,
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = "排行榜",
                        tint = MaterialTheme.colorScheme.primary,
                    )
                }

                Surface(
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(Tokens.Spacing.Large),
                    color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f),
                ) {
                    Row(
                        modifier =
                        Modifier.padding(
                            horizontal = Tokens.Spacing.Medium,
                            vertical = Tokens.Spacing.Small,
                        ),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                    ) {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(Tokens.Spacing.Large),
                        )

                        Text(
                            text = "搜索健身房或用户...",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                        )
                    }
                }

                IconButton(
                    onClick = onThemeToggle,
                ) {
                    Icon(
                        imageVector = Icons.Default.Palette,
                        contentDescription = "主题切换",
                        tint = MaterialTheme.colorScheme.primary,
                    )
                }
            }

            // 核心按钮区域
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
            ) {
                OutlinedButton(
                    onClick = onFindPartner,
                    modifier = Modifier.weight(1f),
                ) {
                    Icon(
                        imageVector = Icons.Default.People,
                        contentDescription = null,
                        modifier = Modifier.size(Tokens.Spacing.Medium),
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.XSmall))
                    Text(
                        text = "寻找伙伴",
                        style = MaterialTheme.typography.labelMedium,
                    )
                }

                Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                OutlinedButton(
                    onClick = onGetLocation,
                    modifier = Modifier.weight(1f),
                ) {
                    Icon(
                        imageVector = Icons.Default.MyLocation,
                        contentDescription = null,
                        modifier = Modifier.size(Tokens.Spacing.Medium),
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.XSmall))
                    Text(
                        text = "定位",
                        style = MaterialTheme.typography.labelMedium,
                    )
                }
            }
        }
    }
}

@GymBroPreview
@Composable
private fun mapInteractionBarPreview() {
    GymBroTheme {
        Box(
            modifier =
            Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surfaceVariant),
        ) {
            MapInteractionBar()
        }
    }
}

@GymBroPreview
@Composable
private fun rankingSectionPreview() {
    GymBroTheme {
        RankingSection()
    }
}

@GymBroPreview
@Composable
private fun searchAndControlSectionPreview() {
    GymBroTheme {
        SearchAndControlSection()
    }
}
