package com.example.gymbro.domain.coach.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.CoachMessage
import kotlinx.coroutines.flow.Flow

/**
 * AI Coach Repository Interface
 * Defines operations related to interacting with the AI coach feature.
 * 阶段4升级：支持多轮对话和会话管理
 */
interface AICoachRepository {
    /**
     * 向AI教练发送消息（支持多轮对话）
     * @param sessionId 会话ID，用于维护对话上下文
     * @param userInput 用户输入的文本消息
     * @return 包含教练回复消息的Flow
     */
    fun sendMessageToCoach(
        sessionId: String,
        userInput: String,
    ): Flow<ModernResult<CoachMessage>>

    /**
     * 向AI教练发送消息并获取原始的流式响应
     *
     * 🔥 新增：接收已构建的消息列表，避免重复prompt构建
     * @param sessionId 会话ID
     * @param messages 已构建的消息列表
     * @return 包含原始字符串token的Flow
     */
    fun getStreamingResponse(
        sessionId: String,
        messages: List<com.example.gymbro.core.ai.prompt.builder.CoreChatMessage>,
    ): Flow<String>

    // 🧹 REMOVED: getStreamingResponseLegacy废弃方法已删除
    // 使用getStreamingResponse(messages)替代，避免重复prompt构建

    /**
     * 获取会话的对话历史记录
     * @param sessionId 会话ID
     * @param limit 获取的消息数量限制，默认20条
     * @return 包含教练对话历史的Flow
     */
    fun getCoachingHistory(
        sessionId: String,
        limit: Int = 20,
    ): Flow<ModernResult<List<CoachMessage>>>

    /**
     * 清空指定会话的对话历史
     * @param sessionId 会话ID
     * @return 清空操作的结果
     */
    suspend fun clearCoachingHistory(sessionId: String): ModernResult<Unit>
}
