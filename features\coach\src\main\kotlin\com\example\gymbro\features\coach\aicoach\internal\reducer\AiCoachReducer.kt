package com.example.gymbro.features.coach.aicoach.internal.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.datetime.Clock
import timber.log.Timber
import javax.inject.Inject

/**
 * 🔥 第3道闸：重复消息ID异常
 */
class DuplicateMessageIdException(
    messageId: String,
) : IllegalStateException("🔥 [第3道闸] 检测到重复消息ID: $messageId，违反唯一性约束")

/**
 * AI Coach Reducer - 处理所有用户意图的状态变更逻辑
 *
 * 遵循MVI架构原则：
 * - 纯函数：给定相同的输入，总是产生相同的输出
 * - 不可变：不修改传入的状态，总是返回新状态
 * - 无副作用：只处理状态变更，不执行异步操作
 */
internal class AiCoachReducer
@Inject
constructor(
    // 🔥 【多轮对话修复】注入MessagingReducerHandler，用于委托消息相关的Intent处理
    private val messagingReducerHandler:
    com.example.gymbro.features.coach.aicoach.internal.reducer.handlers.MessagingReducerHandler,
) : Reducer<AiCoachContract.Intent, AiCoachContract.State, AiCoachContract.Effect> {
    override fun reduce(
        intent: AiCoachContract.Intent,
        state: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        // 🔥 添加 Reducer 入口日志
        Timber.tag(
            "REDUCER-DEBUG",
        ).d("🚀 AiCoachReducer.reduce() 被调用: intent=${intent::class.simpleName}")

        return when (intent) {
            // === 输入处理 ===
            is AiCoachContract.Intent.UpdateInput -> {
                // 🔥 【发送按钮逻辑修复】根据输入内容和AI响应状态动态更新按钮状态
                val newSendButtonState =
                    when {
                        state.streamingState !is AiCoachContract.StreamingState.Idle -> AiCoachContract.SendButtonState.STOP // AI正在响应时显示停止按钮
                        intent.text.isNotBlank() -> AiCoachContract.SendButtonState.SEND // 有文本时显示发送按钮
                        else -> AiCoachContract.SendButtonState.VOICE // 无文本时显示语音按钮
                    }

                // 🔥 【SuggestionChip交互】输入内容时隐藏建议标签
                val shouldHideSuggestionChips = intent.text.isNotBlank()

                ReduceResult(
                    state.copy(
                        inputState =
                        state.inputState.copy(
                            text = intent.text,
                            sendButtonState = newSendButtonState,
                        ),
                        suggestionChipsVisible = if (shouldHideSuggestionChips) false else state.suggestionChipsVisible,
                    ),
                )
            }

            // === 消息发送 ===
            is AiCoachContract.Intent.SendMessage -> {
                // 🔥 【多轮对话修复】委托给MessagingReducerHandler处理，确保messageId一致性
                Timber.tag("REDUCER-DEBUG").d(
                    "🚀 委托 SendMessage Intent 给 MessagingReducerHandler: content=${
                        intent.content.take(100)
                    }${if (intent.content.length > 100) "..." else ""}",
                )
                messagingReducerHandler.handle(intent, state)
            }

            // === 🔥 【Plan B架构】用户消息发送状态通知 ===
            is AiCoachContract.Intent.UserMessageSent -> {
                Timber.tag("PLAN-B").d(
                    "📤 [UserMessageSent] 用户消息已发送: messageId=${intent.messageId}, sessionId=${intent.sessionId}"
                )
                // 这个Intent主要用于激活ThinkingBox的ThinkingHeader显示条件
                // Coach模块只记录状态，不需要额外处理
                ReduceResult.stateOnly(state)
            }

            // === 🔥 清理：禁用自动初始化，只响应用户操作 ===
            is AiCoachContract.Intent.LoadInitialData -> {
                // 🔥 清理：不再自动加载会话，只设置基础状态
                Timber.d("🔍 [LoadInitialData] 跳过自动会话加载，等待用户操作")
                ReduceResult.stateOnly(state.copy(isLoading = false))
            }

            // === 🔥 清理：禁用自动会话加载 ===
            is AiCoachContract.Intent.LoadInitialSession -> {
                // 🔥 清理：不再自动加载会话，只响应用户明确操作
                Timber.d("🔍 [LoadInitialSession] 跳过自动会话加载，等待用户操作")
                ReduceResult.stateOnly(state.copy(isLoading = false))
            }

            // 🔥 【架构清理】UpdateStreamingMessage Intent 已完全移除
            // Coach 不再处理任何流式消息内容，全部由 ThinkingBox 负责

            // === 重置流式状态 ===
            is AiCoachContract.Intent.ResetStreamingState -> {
                Timber.d("🔄 重置流式状态")

                // 🔥 关键修复：同时重置发送按钮状态
                val newSendButtonState =
                    if (state.inputState.text.isNotBlank()) {
                        AiCoachContract.SendButtonState.SEND
                    } else {
                        AiCoachContract.SendButtonState.VOICE
                    }

                Timber.d("🔄 重置后的按钮状态: $newSendButtonState, 输入文本: '${state.inputState.text}'")

                ReduceResult.stateOnly(
                    state.copy(
                        // 🔥 【架构重构】重置流式状态为 Idle
                        streamingState = AiCoachContract.StreamingState.Idle,
                        isLoading = false,
                        inputState =
                        state.inputState.copy(
                            sendButtonState = newSendButtonState,
                        ),
                    ),
                )
            }

            // === 停止生成处理 ===
            is AiCoachContract.Intent.OnStopGenerationClick -> {
                Timber.d("🛑 用户点击停止生成")

                // 🔥 关键修复：重置所有相关状态
                val newSendButtonState =
                    if (state.inputState.text.isNotBlank()) {
                        AiCoachContract.SendButtonState.SEND
                    } else {
                        AiCoachContract.SendButtonState.VOICE
                    }

                ReduceResult.stateOnly(
                    state.copy(
                        // 🔥 【架构重构】重置流式状态为 Idle
                        streamingState = AiCoachContract.StreamingState.Idle,
                        isLoading = false,
                        inputState =
                        state.inputState.copy(
                            sendButtonState = newSendButtonState,
                        ),
                    ),
                )
            }

            // === 语音输入 ===
            is AiCoachContract.Intent.StartVoiceInput -> {
                val newInputState = state.inputState.copy(voiceRecording = true)
                ReduceResult(
                    newState = state.copy(inputState = newInputState),
                    effects = emptyList(),
                )
            }

            is AiCoachContract.Intent.StopVoiceInput -> {
                val newInputState = state.inputState.copy(voiceRecording = false)
                ReduceResult(
                    newState = state.copy(inputState = newInputState),
                    effects = emptyList(),
                )
            }

            // === 清空输入 ===
            is AiCoachContract.Intent.ClearInput -> {
                val newInputState = state.inputState.copy(text = "")
                ReduceResult(
                    newState = state.copy(inputState = newInputState),
                    effects = emptyList(),
                )
            }

            // === 🔥 会话管理 ===
            is AiCoachContract.Intent.CreateNewSession ->
                ReduceResult.withEffect(
                    state.copy(isLoading = true),
                    AiCoachContract.Effect.CreateNewSession,
                )

            is AiCoachContract.Intent.SwitchSession ->
                ReduceResult.withEffect(
                    state.copy(
                        isLoading = true,
                        messages = persistentListOf(), // 清空消息以避免UI key冲突
                        streamingState = AiCoachContract.StreamingState.Idle,
                    ),
                    AiCoachContract.Effect.SwitchSession(intent.sessionId),
                )

            // === 🔥 【消息迁移修复】刷新历史消息流 ===
            is AiCoachContract.Intent.RefreshHistoryFlow -> {
                Timber.d("🔄 [RefreshHistoryFlow] 刷新历史消息流: sessionId=${intent.sessionId}")

                ReduceResult(
                    newState = state,
                    effects =
                    listOf(
                        AiCoachContract.Effect.SetupHistoryFlow(intent.sessionId),
                    ),
                )
            }

            // === 🔥 API和Prompt模式切换 ===
            is AiCoachContract.Intent.SwitchPromptMode -> {
                ReduceResult(
                    newState = state.copy(currentPromptMode = intent.mode),
                    effects = listOf(AiCoachContract.Effect.SwitchPromptMode(intent.mode)),
                )
            }

            is AiCoachContract.Intent.SwitchApiProvider -> {
                ReduceResult(
                    newState = state.copy(currentApiProvider = intent.provider),
                    effects = listOf(AiCoachContract.Effect.SwitchApiProvider(intent.provider)),
                )
            }

            // === 🔥 网络状态管理 ===
            is AiCoachContract.Intent.NetworkEventReceived -> {
                // 网络事件处理逻辑
                ReduceResult.stateOnly(state)
            }

            // 🔥 【P0修复】移除ToggleThinkingViewMode Intent - 已在P0阶段删除

            // 🔥 移除：ThinkingBox状态更新已剔除

            // === 🔥 工具面板切换 ===
            is AiCoachContract.Intent.OnToolsClick -> {
                ReduceResult(
                    newState =
                    state.copy(
                        inputState =
                        state.inputState.copy(
                            isToolbarExpanded = !state.inputState.isToolbarExpanded,
                        ),
                    ),
                )
            }

            // === 🔥 工具选择处理 ===
            is AiCoachContract.Intent.OnToolSelected -> {
                // 🔥 【架构重构】Function Call消息构建移到ViewModel中处理
                // Reducer只负责状态变更，不处理字符串资源
                ReduceResult(
                    newState = state.copy(
                        inputState = state.inputState.copy(
                            isToolbarExpanded = false, // 关闭工具面板
                        ),
                    ),
                    effects = listOf(
                        AiCoachContract.Effect.BuildFunctionCallMessage(intent.functionCallName),
                    ),
                )
            }

            // === 🔥 Function Call完成处理 - V2 (UI弹窗) ===
            is AiCoachContract.Intent.FunctionCallCompleted -> {
                // 1. 创建一个简单的文本消息，通知用户操作已完成
                val resultMessageId = generateMessageId()
                val existingIds = state.messages.map { it.id }.toSet()
                if (existingIds.contains(resultMessageId)) {
                    throw DuplicateMessageIdException(resultMessageId)
                }

                val resultMessage = AiCoachContract.MessageUi(
                    id = resultMessageId,
                    content = intent.result.data ?: "工具执行完成。",
                    isFromUser = false,
                    timestamp = Clock.System.now(),
                    saveStatus = AiCoachContract.SaveStatus.SAVED, // 这是瞬时消息，直接标记为已保存
                )
                val newMessages = (state.messages + resultMessage).toImmutableList()

                // 2. 将完整结果设置到 activeFunctionCall 来触发UI弹窗
                ReduceResult(
                    newState =
                    state.copy(
                        messages = newMessages,
                        isLoading = false,
                        activeFunctionCall = intent.result, // 🔥 核心改动：触发弹窗
                        // 🔥 恢复按钮状态
                        inputState =
                        state.inputState.copy(
                            sendButtonState =
                            if (state.inputState.text.isNotBlank()) {
                                AiCoachContract.SendButtonState.SEND
                            } else {
                                AiCoachContract.SendButtonState.VOICE
                            },
                        ),
                    ),
                )
            }

            // === 🔥 Function Call调试处理 ===
            is AiCoachContract.Intent.DismissFunctionCallResult -> {
                ReduceResult.stateOnly(
                    state.copy(activeFunctionCall = null),
                )
            }

            // 🔥 新增：处理调试Intent
            is AiCoachContract.Intent.DebugTriggerFunctionCallResult -> {
                Timber.tag("DEBUG-FC").d("触发模拟Function Call: ${intent.result.functionName}")
                ReduceResult.stateOnly(
                    state.copy(activeFunctionCall = intent.result),
                )
            }

            // 🔥 新增：执行真实的Function Call调试测试
            is AiCoachContract.Intent.DebugExecuteRealFunctionCall -> {
                Timber.tag("DEBUG-FC").d("🚀 执行真实Function Call: ${intent.functionName}")
                ReduceResult(
                    newState = state.copy(isLoading = true),
                    effects = listOf(
                        AiCoachContract.Effect.ExecuteFunctionCall(
                            functionCallName = intent.functionName,
                            arguments = com.example.gymbro.features.coach.internal.debug.DebugTestDataSets.getDebugArgumentsForFunction(
                                intent.functionName,
                            ),
                            sessionId = state.activeSession?.id ?: "debug_session",
                        ),
                    ),
                )
            }

            // === 🔥 导航Intent处理 ===
            is AiCoachContract.Intent.NavigateToWorkout -> {
                Timber.d("🎯 处理导航Intent: 跳转到训练页面")
                ReduceResult(
                    newState = state,
                    effects = listOf(AiCoachContract.Effect.NavigateToWorkout),
                )
            }

            is AiCoachContract.Intent.NavigateToTemplates -> {
                Timber.d("🎯 处理导航Intent: 跳转到模板页面")
                ReduceResult(
                    newState = state,
                    effects = listOf(AiCoachContract.Effect.NavigateToTemplates),
                )
            }

            is AiCoachContract.Intent.NavigateToPlans -> {
                Timber.d("🎯 处理导航Intent: 跳转到计划页面")
                ReduceResult(
                    newState = state,
                    effects = listOf(AiCoachContract.Effect.NavigateToPlans),
                )
            }

            is AiCoachContract.Intent.NavigateToExerciseLibrary -> {
                Timber.d("🎯 处理导航Intent: 跳转到动作库页面")
                ReduceResult(
                    newState = state,
                    effects = listOf(AiCoachContract.Effect.NavigateToExerciseLibrary),
                )
            }

            // === 🔥 图片选择器显示/隐藏处理 ===
            is AiCoachContract.Intent.ShowImagePicker -> {
                ReduceResult(
                    state.copy(
                        isImagePickerVisible = true,
                    ),
                )
            }

            is AiCoachContract.Intent.HideImagePicker -> {
                ReduceResult(
                    state.copy(
                        isImagePickerVisible = false,
                    ),
                )
            }

            // === 🔥 【独立SummaryCard设计】SummaryCard管理 ===
            is AiCoachContract.Intent.ShowSummaryCard -> {
                Timber.d("🔄 [ShowSummaryCard] 显示SummaryCard: messageId=${intent.messageId}")

                // 获取对应的思考内容
                // 🔥 【P0修复】移除thinkingState引用 - Coach不再直接访问ThinkingBox状态
                // ThinkingBox现在独立管理状态，SummaryCard功能需要重新设计
                Timber.d("🔄 [ShowSummaryCard] ThinkingBox状态现在独立管理，SummaryCard功能暂时禁用")

                // 🔥 【P0修复】暂时禁用SummaryCard功能，返回当前状态
                // 未来需要通过ThinkingBoxApi获取思考内容
                val newSummaryCardState = state.summaryCardState

                Timber.d(
                    "🔄 [ShowSummaryCard] 创建SummaryCardState: isVisible=${newSummaryCardState.isVisible}, messageId=${newSummaryCardState.messageId}",
                )

                ReduceResult.stateOnly(
                    state.copy(summaryCardState = newSummaryCardState),
                )
            }

            is AiCoachContract.Intent.HideSummaryCard -> {
                Timber.d("🔄 [HideSummaryCard] 隐藏SummaryCard并返回主屏幕状态")

                // 🔥 【导航修复】隐藏SummaryCard时，确保返回到正常的聊天状态
                // 这包括：
                // 1. 隐藏SummaryCard
                // 2. 恢复正常的UI状态
                // 3. 确保用户可以继续正常使用聊天功能
                ReduceResult.stateOnly(
                    state.copy(
                        summaryCardState = com.example.gymbro.features.coach.aicoach.SummaryCardState(),
                        // 🔥 确保工具栏收起，回到正常输入状态
                        inputState = state.inputState.copy(
                            isToolbarExpanded = false, // 关闭工具栏
                        ),
                        // 🔥 确保建议标签可见（如果输入为空）
                        suggestionChipsVisible = state.inputState.text.isBlank(),
                    ),
                )
            }

            // === 🔥 图片选择处理 ===
            is AiCoachContract.Intent.OnImagesSelected -> {
                // 将 Uri 转换为 String 并添加到已选择的图片列表
                val newImageUrls = intent.uris.map { it.toString() }
                val updatedImages = (state.inputState.selectedImages + newImageUrls).toImmutableList()
                ReduceResult(
                    state.copy(
                        inputState =
                        state.inputState.copy(
                            selectedImages = updatedImages,
                        ),
                        isImagePickerVisible = false, // 选择完图片后自动隐藏面板
                    ),
                )
            }

            // === 🔥 图片移除处理 ===
            is AiCoachContract.Intent.OnRemoveImageClick -> {
                val updatedImages = state.inputState.selectedImages.filter { it != intent.imageUrl }
                ReduceResult(
                    state.copy(
                        inputState =
                        state.inputState.copy(
                            selectedImages = updatedImages.toImmutableList(),
                        ),
                    ),
                )
            }

            // === 🔥 AI消息保存处理 - 修复历史记录缺失问题 ===
            is AiCoachContract.Intent.SaveAiMessage -> {
                Timber.d(
                    "💾 处理AI消息保存Intent: sessionId=${intent.sessionId}, messageId=${intent.messageId}",
                )
                Timber.d(
                    "💾 [固化AI响应] finalMarkdown长度=${intent.finalMarkdown?.length}, content长度=${intent.content.length}",
                )

                // 检查activeSession是否存在
                if (state.activeSession == null) {
                    Timber.w("⚠️ 无法保存AI消息：activeSession为null，跳过SaveAiMessage")
                    return ReduceResult.stateOnly(state)
                }

                // 确保使用activeSession.id，而不是intent中可能不正确的sessionId
                val sessionId = state.activeSession.id

                // 记录日志以便调试
                if (sessionId != intent.sessionId) {
                    Timber.w("⚠️ 修正sessionId不匹配: intent提供=${intent.sessionId}，实际使用=$sessionId")
                }

                // 🔥 【关键修复】更新UI中的AI消息内容和finalMarkdown
                val updatedMessages =
                    state.messages
                        .map { message ->
                            if (message.id == intent.messageId) { // 🔥 【ID统一】使用messageId
                                Timber.d(
                                    "💾 [固化AI响应] 更新消息: id=${message.id}, 原content长度=${message.content.length}, 新content长度=${intent.content.length}",
                                )
                                message.copy(
                                    content = intent.content,
                                    finalMarkdown = intent.finalMarkdown,
                                    saveStatus = AiCoachContract.SaveStatus.SAVING, // 设置为保存中
                                )
                            } else {
                                message
                            }
                        }.toImmutableList()

                ReduceResult(
                    newState =
                    state.copy(
                        messages = updatedMessages,
                        streamingState = AiCoachContract.StreamingState.Idle, // 重置流式状态
                    ),
                    effects =
                    listOf(
                        AiCoachContract.Effect.SaveAiMessage(
                            sessionId = sessionId,
                            messageId = intent.messageId, // 🔥 【ID统一】使用messageId
                            content = intent.content,
                            inReplyToMessageId = intent.inReplyToMessageId,
                            finalMarkdown = intent.finalMarkdown,
                            // 🔥 【P0修复】移除thinkingNodes字段
                        ),
                    ),
                )
            }

            // 🔥 【单一数据源修复】移除重复的SaveAiMessageWithThinking处理
            // 统一使用SaveAiMessage Intent，包含ThinkingBox字段

            // === 🔥 历史记录面板切换 ===
            is AiCoachContract.Intent.ToggleHistoryPanel -> {
                Timber.d("🔥 [ToggleHistoryPanel] 切换历史面板状态: 当前=${state.historyState.isVisible}")

                ReduceResult(
                    newState =
                    state.copy(
                        historyState =
                        state.historyState.copy(
                            isVisible = !state.historyState.isVisible,
                        ),
                    ),
                )
            }

            // === 🔥 【消息保存修复】消息保存状态更新 ===
            is AiCoachContract.Intent.UpdateMessageSaveStatus -> {
                Timber.d(
                    "🔥 [UpdateMessageSaveStatus] 更新消息保存状态: messageId=${intent.messageId}, status=${intent.saveStatus}",
                )
                Timber
                    .tag("MESSAGE-SAVE")
                    .d(
                        "🔄 [UpdateMessageSaveStatus] 状态更新: messageId=${intent.messageId}, ${intent.saveStatus}",
                    )

                // 🔥 【关键修复】确保消息存在才更新状态
                val messageExists = state.messages.any { it.id == intent.messageId }
                if (!messageExists) {
                    Timber
                        .tag("MESSAGE-SAVE")
                        .w("⚠️ [UpdateMessageSaveStatus] 消息不存在: messageId=${intent.messageId}")
                    return@reduce ReduceResult.stateOnly(state)
                }

                val updatedMessages =
                    state.messages
                        .map { message ->
                            if (message.id == intent.messageId) {
                                Timber
                                    .tag("MESSAGE-SAVE")
                                    .d(
                                        "✅ [UpdateMessageSaveStatus] 更新消息状态: messageId=${intent.messageId}, ${message.saveStatus} → ${intent.saveStatus}",
                                    )
                                message.copy(saveStatus = intent.saveStatus)
                            } else {
                                message
                            }
                        }.toImmutableList()

                ReduceResult(
                    newState = state.copy(messages = updatedMessages),
                )
            }

            // === 🔥 【历史导航修复】加载状态管理 ===
            is AiCoachContract.Intent.UpdateLoadingState -> {
                Timber.tag("HISTORY-NAV").d("🔄 [UpdateLoadingState] 更新加载状态: ${intent.isLoading}")
                ReduceResult(
                    newState = state.copy(isLoading = intent.isLoading),
                )
            }

            // === 🔥 【历史导航修复】会话加载 Intent 处理 ===
            is AiCoachContract.Intent.SessionWithMessagesLoaded -> {
                Timber
                    .tag("HISTORY-NAV")
                    .i(
                        "🔄 [SessionWithMessagesLoaded] 处理会话加载: sessionId=${intent.session.id}, messages=${intent.messages.size}",
                    )
                ReduceResult(
                    newState =
                    state.copy(
                        isLoading = false,
                        activeSession = intent.session,
                        messages = intent.messages,
                    ),
                    effects = listOf(AiCoachContract.Effect.SetupHistoryFlow(intent.session.id)),
                )
            }

            // === 新增：结果型 Intent 的处理逻辑 ===
            is AiCoachContract.Intent.SessionCreatedResult -> handleSessionCreatedResult(intent, state)
            is AiCoachContract.Intent.SessionWithMessagesLoadedResult ->
                handleSessionWithMessagesLoadedResult(
                    intent,
                    state,
                )

            is AiCoachContract.Intent.SessionsLoadedResult -> handleSessionsLoadedResult(intent, state)
            is AiCoachContract.Intent.MessageSaveCompletedResult ->
                handleMessageSaveCompletedResult(
                    intent,
                    state,
                )

            is AiCoachContract.Intent.SuggestionConfigLoadedResult ->
                handleSuggestionConfigLoadedResult(
                    intent,
                    state,
                )

            is AiCoachContract.Intent.SuggestionConfigFailedResult ->
                handleSuggestionConfigFailedResult(
                    intent,
                    state,
                )

            is AiCoachContract.Intent.FunctionCallProcessedResult ->
                handleFunctionCallProcessedResult(
                    intent,
                    state,
                )
            // ... 其他结果型 Intent 的处理器

            // 🔥 【P0修复】移除ThinkingBox状态更新处理 - Coach不再管理ThinkingBox状态

            // === 建议标签交互 ===
            is AiCoachContract.Intent.HideSuggestionChips -> {
                ReduceResult(
                    state.copy(suggestionChipsVisible = false),
                )
            }

            // === 🔥 Coach-ThinkingBox重构：AI请求发送相关 Intent ===
            is AiCoachContract.Intent.SendAiRequest -> {
                Timber.d("🚀 [SendAiRequest] 处理AI请求发送: messageId=${intent.messageId}")

                // 创建用户消息并添加到状态
                val userMessage = AiCoachContract.MessageUi(
                    id = intent.messageId,
                    content = intent.userInput,
                    isFromUser = true,
                    timestamp = Clock.System.now(),
                )

                val updatedMessages = (state.messages + userMessage).toImmutableList()

                // 构建对话历史（配对用户和助手消息）
                val conversationHistory =
                    mutableListOf<com.example.gymbro.core.ai.prompt.builder.ConversationTurn>()
                val messages = state.messages
                var i = 0
                while (i < messages.size - 1) {
                    val currentMessage = messages[i]
                    val nextMessage = messages[i + 1]

                    // 检查是否为用户消息后跟AI消息的模式
                    if (currentMessage.isFromUser && !nextMessage.isFromUser) {
                        conversationHistory.add(
                            com.example.gymbro.core.ai.prompt.builder.ConversationTurn(
                                user = currentMessage.content,
                                assistant = nextMessage.content,
                            ),
                        )
                        i += 2 // 跳过已配对的两条消息
                    } else {
                        i++ // 如果不匹配，继续寻找
                    }
                }

                ReduceResult(
                    newState = state.copy(
                        messages = updatedMessages,
                        isLoading = true,
                        inputState = state.inputState.copy(
                            text = "", // 清空输入
                            sendButtonState = AiCoachContract.SendButtonState.STOP,
                        ),
                    ),
                    effects = listOf(
                        AiCoachContract.Effect.BuildAndSendPrompt(
                            messageId = intent.messageId,
                            userInput = intent.userInput,
                            conversationHistory = conversationHistory,
                        ),
                    ),
                )
            }

            is AiCoachContract.Intent.ThinkingBoxCompleted -> {
                Timber.d("✅ [ThinkingBoxCompleted] ThinkingBox显示完成: messageId=${intent.messageId}")

                // 创建AI响应消息
                val aiMessage = AiCoachContract.MessageUi(
                    id = generateMessageId(),
                    content = intent.finalContent,
                    isFromUser = false,
                    timestamp = Clock.System.now(),
                    thinkingProcess = intent.thinkingProcess,
                )

                val updatedMessages = (state.messages + aiMessage).toImmutableList()

                ReduceResult(
                    newState = state.copy(
                        messages = updatedMessages,
                        isLoading = false,
                        inputState = state.inputState.copy(
                            sendButtonState = if (state.inputState.text.isNotBlank()) {
                                AiCoachContract.SendButtonState.SEND
                            } else {
                                AiCoachContract.SendButtonState.VOICE
                            },
                        ),
                    ),
                )
            }

            is AiCoachContract.Intent.ThinkingBoxFailed -> {
                Timber.e(
                    "❌ [ThinkingBoxFailed] ThinkingBox显示失败: messageId=${intent.messageId}, error=${intent.error.message}",
                )

                ReduceResult(
                    newState = state.copy(
                        isLoading = false,
                        inputState = state.inputState.copy(
                            sendButtonState = if (state.inputState.text.isNotBlank()) {
                                AiCoachContract.SendButtonState.SEND
                            } else {
                                AiCoachContract.SendButtonState.VOICE
                            },
                        ),
                    ),
                    effects = listOf(
                        AiCoachContract.Effect.ShowError(
                            error = "AI响应处理失败: ${intent.error.message}",
                        ),
                    ),
                )
            }

            // === 其他Intent暂时返回原状态 ===
            else -> {
                ReduceResult(state, emptyList())
            }
        }
    }

    /**
     * 生成唯一的消息ID
     * 🔥 修复：统一使用UUID确保绝对唯一性，避免时间戳冲突
     */
    private fun generateMessageId(): String =
        com.example.gymbro.core.util.Constants.MessageId
            .generate()

    // 🔥 已移除：getDebugArgumentsForFunction 方法
    // 现在使用 DebugTestDataSets.getDebugArgumentsForFunction() 替代

    // 🔥 【架构重构】buildFunctionCallMessage函数已移除
    // 现在使用 domain/coach/usecase/BuildFunctionCallMessageUseCase 处理Function Call消息构建
    // 通过 AiCoachContract.Effect.BuildFunctionCallMessage 在ViewModel中调用

    /**
     * 查找最后一条用户消息的ID
     */
    private fun findLastUserMessageId(
        messages: List<AiCoachContract.MessageUi>,
    ): String = messages.lastOrNull { it.isFromUser }?.id ?: ""

    // =================================================================================
    // === 🔥 新增：结果型 Intent 处理器 ===
    // =================================================================================

    private fun handleSessionCreatedResult(
        intent: AiCoachContract.Intent.SessionCreatedResult,
        state: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        val newSession = intent.session
        Timber
            .tag("HISTORY-FIX")
            .d(
                "✅ [Phase1] Reducer处理SessionCreatedResult: sessionId=${newSession.id}, title=${newSession.title}",
            )
        Timber.d("✅ 处理 SessionCreatedResult: sessionId=${newSession.id}")

        val hasPendingMessage = !state.pendingMessage.isNullOrBlank()
        Timber.tag("HISTORY-FIX").d("✅ [Phase1] 待发消息状态: hasPendingMessage=$hasPendingMessage")

        val newState =
            state.copy(
                activeSession = newSession,
                isLoading = false, // 🔥 确保加载状态被清除
                messages = persistentListOf(), // 🔥 清空消息列表
                errorCode = null, // 🔥 清除错误状态
                pendingMessage = null, // 清除待发消息
                suggestionChipsVisible = true, // 🔥 【SuggestionChip交互】新会话时重置建议标签显示状态
            )

        Timber
            .tag("HISTORY-FIX")
            .d(
                "✅ [Phase1] 新状态: isLoading=${newState.isLoading}, activeSession=${newState.activeSession?.id}, messages=${newState.messages.size}",
            )

        // 如果有待发消息，立即发送
        if (hasPendingMessage) {
            return messagingReducerHandler.handle(
                AiCoachContract.Intent.SendMessage(state.pendingMessage!!),
                newState,
            )
        }

        return ReduceResult(newState, listOf(AiCoachContract.Effect.SetupHistoryFlow(newSession.id)))
    }

    private fun handleSessionWithMessagesLoadedResult(
        intent: AiCoachContract.Intent.SessionWithMessagesLoadedResult,
        state: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        Timber.d("✅ 处理 SessionWithMessagesLoadedResult: sessionId=${intent.session.id}")
        return ReduceResult(
            state.copy(
                isLoading = false,
                activeSession = intent.session,
                messages = intent.messages,
            ),
            listOf(AiCoachContract.Effect.SetupHistoryFlow(intent.session.id)),
        )
    }

    private fun handleSessionsLoadedResult(
        intent: AiCoachContract.Intent.SessionsLoadedResult,
        state: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            state.copy(
                sessionState =
                state.sessionState.copy(
                    allSessions = intent.sessions,
                    isLoadingSessions = false,
                ),
            ),
        )

    private fun handleMessageSaveCompletedResult(
        intent: AiCoachContract.Intent.MessageSaveCompletedResult,
        state: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        Timber.d(
            "✅ 处理 MessageSaveCompletedResult: messageId=${intent.messageId}, success=${intent.success}",
        )
        Timber
            .tag("MESSAGE-SAVE")
            .d(
                "🏁 [MessageSaveCompletedResult] 保存完成: messageId=${intent.messageId}, success=${intent.success}",
            )

        // 🔥 【关键修复】确保消息存在才更新状态
        val messageExists = state.messages.any { it.id == intent.messageId }
        if (!messageExists) {
            Timber
                .tag("MESSAGE-SAVE")
                .w("⚠️ [MessageSaveCompletedResult] 消息不存在: messageId=${intent.messageId}")
            return ReduceResult.stateOnly(state)
        }

        val newStatus =
            if (intent.success) AiCoachContract.SaveStatus.SAVED else AiCoachContract.SaveStatus.FAILED
        val updatedMessages =
            state.messages
                .map { message ->
                    if (message.id == intent.messageId) {
                        Timber
                            .tag("MESSAGE-SAVE")
                            .d(
                                "✅ [MessageSaveCompletedResult] 最终状态更新: messageId=${intent.messageId}, ${message.saveStatus} → $newStatus",
                            )
                        message.copy(saveStatus = newStatus)
                    } else {
                        message
                    }
                }.toImmutableList()

        return ReduceResult.stateOnly(state.copy(messages = updatedMessages))
    }

    private fun handleSuggestionConfigLoadedResult(
        intent: AiCoachContract.Intent.SuggestionConfigLoadedResult,
        state: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> = ReduceResult.stateOnly(
        state.copy(suggestionConfig = intent.config),
    )

    private fun handleSuggestionConfigFailedResult(
        intent: AiCoachContract.Intent.SuggestionConfigFailedResult,
        state: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        Timber.e(intent.error, "加载建议配置失败")
        return ReduceResult.stateOnly(state) // 非关键功能，只记录日志
    }

    private fun handleFunctionCallProcessedResult(
        intent: AiCoachContract.Intent.FunctionCallProcessedResult,
        state: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        val resultMessageId = generateMessageId()
        val resultMessage =
            AiCoachContract.MessageUi(
                id = resultMessageId,
                content = intent.result.data ?: "工具执行完成",
                isFromUser = false,
                timestamp = Clock.System.now(),
                functionCallResult = intent.result,
            )

        val updatedMessages = (state.messages + resultMessage).toImmutableList()

        return ReduceResult(
            newState =
            state.copy(
                messages = updatedMessages,
                isLoading = false,
                inputState =
                state.inputState.copy(
                    sendButtonState =
                    if (state.inputState.text.isNotBlank()) {
                        AiCoachContract.SendButtonState.SEND
                    } else {
                        AiCoachContract.SendButtonState.VOICE
                    },
                ),
            ),
        )
    }
}
