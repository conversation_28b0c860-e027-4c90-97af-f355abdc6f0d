package com.example.gymbro.designSystem.theme.tokens

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Assert.*
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * Token使用正确性验证测试
 *
 * 测试覆盖：
 * - Token系统完整性验证
 * - 颜色Token主题兼容性测试
 * - 间距Token一致性测试
 * - 圆角Token层次结构测试
 * - 字体Token可读性测试
 * - 硬编码值检测测试
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [31])
class TokenUsageValidationTest {

    // === 颜色Token测试 ===

    @Test
    fun `颜色Token应该存在且不为null`() {
        // 主要颜色Token
        assertNotNull("BrandPrimary应该存在", Tokens.Color.BrandPrimary)
        assertNotNull("BrandSecondary应该存在", Tokens.Color.BrandSecondary)
        assertNotNull("CTAPrimary应该存在", Tokens.Color.CTAPrimary)

        // 功能性颜色Token
        assertNotNull("Success应该存在", Tokens.Color.Success)
        assertNotNull("Warning应该存在", Tokens.Color.Warning)
        assertNotNull("Error应该存在", Tokens.Color.Error)
        assertNotNull("Info应该存在", Tokens.Color.Info)
    }

    @Test
    fun `灰阶Token应该有正确的层次结构`() {
        // 检查13级灰阶系统的存在
        assertNotNull("Gray000应该存在", Tokens.Color.Gray000)
        assertNotNull("Gray050应该存在", Tokens.Color.Gray050)
        assertNotNull("Gray100应该存在", Tokens.Color.Gray100)
        assertNotNull("Gray200应该存在", Tokens.Color.Gray200)
        assertNotNull("Gray300应该存在", Tokens.Color.Gray300)
        assertNotNull("Gray400应该存在", Tokens.Color.Gray400)
        assertNotNull("Gray500应该存在", Tokens.Color.Gray500)
        assertNotNull("Gray600应该存在", Tokens.Color.Gray600)
        assertNotNull("Gray700应该存在", Tokens.Color.Gray700)
        assertNotNull("Gray800应该存在", Tokens.Color.Gray800)
        assertNotNull("Gray850应该存在", Tokens.Color.Gray850)
        assertNotNull("Gray900应该存在", Tokens.Color.Gray900)
        assertNotNull("Gray950应该存在", Tokens.Color.Gray950)

        // 验证不是同一个颜色值
        assertNotEquals("Gray000和Gray950应该不同", Tokens.Color.Gray000, Tokens.Color.Gray950)
        assertNotEquals("Gray500不应该等于Gray000", Tokens.Color.Gray500, Tokens.Color.Gray000)
    }

    @Test
    fun `深色主题颜色Token应该适合深色背景`() {
        // 深色主题的背景应该是深色
        assertTrue(
            "深色主题背景应该足够深",
            isDarkColor(Tokens.Color.Dark.Background),
        )

        // 深色主题的文本应该是浅色
        assertTrue(
            "深色主题文本应该足够浅以在深背景上可读",
            isLightColor(Tokens.Color.Dark.OnBackground),
        )

        // 验证对比度
        assertNotEquals(
            "深色主题背景和前景应该有足够对比度",
            Tokens.Color.Dark.Background,
            Tokens.Color.Dark.OnBackground,
        )
    }

    @Test
    fun `浅色主题颜色Token应该适合浅色背景`() {
        // 浅色主题的背景应该是浅色
        assertTrue(
            "浅色主题背景应该足够浅",
            isLightColor(Tokens.Color.Light.Background),
        )

        // 浅色主题的文本应该是深色
        assertTrue(
            "浅色主题文本应该足够深以在浅背景上可读",
            isDarkColor(Tokens.Color.Light.OnBackground),
        )

        // 验证对比度
        assertNotEquals(
            "浅色主题背景和前景应该有足够对比度",
            Tokens.Color.Light.Background,
            Tokens.Color.Light.OnBackground,
        )
    }

    // === 间距Token测试 ===

    @Test
    fun `间距Token应该有正确的层次结构`() {
        // 间距应该递增
        assertTrue("Tiny < XSmall", Tokens.Spacing.Tiny < Tokens.Spacing.XSmall)
        assertTrue("XSmall < Small", Tokens.Spacing.XSmall < Tokens.Spacing.Small)
        assertTrue("Small < Medium", Tokens.Spacing.Small < Tokens.Spacing.Medium)
        assertTrue("Medium < Large", Tokens.Spacing.Medium < Tokens.Spacing.Large)
        assertTrue("Large < XLarge", Tokens.Spacing.Large < Tokens.Spacing.XLarge)
        assertTrue("XLarge < XXLarge", Tokens.Spacing.XLarge < Tokens.Spacing.XXLarge)
        assertTrue("XXLarge < Huge", Tokens.Spacing.XXLarge < Tokens.Spacing.Huge)
        assertTrue("Huge < Massive", Tokens.Spacing.Huge < Tokens.Spacing.Massive)
    }

    @Test
    fun `间距Token应该使用合理的值`() {
        // 最小间距不应该为负
        assertTrue("None间距应该为0", Tokens.Spacing.None == 0.dp)
        assertTrue("Tiny间距应该大于0", Tokens.Spacing.Tiny > 0.dp)

        // 中等间距应该是16dp（Material Design标准）
        assertEquals("Medium间距应该是16dp", 16.dp, Tokens.Spacing.Medium)

        // 巨大间距不应该过大
        assertTrue("Massive间距应该小于200dp", Tokens.Spacing.Massive < 200.dp)
    }

    @Test
    fun `语义化间距Token应该映射到基础间距`() {
        // 验证语义化间距的映射
        assertEquals("ComponentPadding应该等于Medium", Tokens.Spacing.Medium, Tokens.Spacing.ComponentPadding)
        assertEquals("SectionSpacing应该等于Large", Tokens.Spacing.Large, Tokens.Spacing.SectionSpacing)
        assertEquals("PageMargin应该等于Medium", Tokens.Spacing.Medium, Tokens.Spacing.PageMargin)
    }

    // === 圆角Token测试 ===

    @Test
    fun `圆角Token应该有正确的层次结构`() {
        // 圆角应该递增
        assertTrue("None < Tiny", Tokens.Radius.None < Tokens.Radius.Tiny)
        assertTrue("Tiny < XSmall", Tokens.Radius.Tiny < Tokens.Radius.XSmall)
        assertTrue("XSmall < Small", Tokens.Radius.XSmall < Tokens.Radius.Small)
        assertTrue("Small < Medium", Tokens.Radius.Small < Tokens.Radius.Medium)
        assertTrue("Medium < Large", Tokens.Radius.Medium < Tokens.Radius.Large)
        assertTrue("Large < XLarge", Tokens.Radius.Large < Tokens.Radius.XLarge)
        assertTrue("XLarge < XXLarge", Tokens.Radius.XLarge < Tokens.Radius.XXLarge)
    }

    @Test
    fun `圆角Token应该使用合理的值`() {
        // 无圆角应该是0
        assertEquals("None圆角应该为0", 0.dp, Tokens.Radius.None)

        // 完全圆形应该是100dp
        assertEquals("Round圆角应该为100dp", 100.dp, Tokens.Radius.Round)

        // 卡片圆角应该符合设计要求（24dp）
        assertEquals("Card圆角应该为24dp", 24.dp, Tokens.Radius.Card)
    }

    @Test
    fun `语义化圆角Token应该映射到基础圆角`() {
        // 验证语义化圆角的映射
        assertEquals("Button圆角应该等于Small", Tokens.Radius.Small, Tokens.Radius.Button)
        assertEquals("Card圆角应该等于XLarge", Tokens.Radius.XLarge, Tokens.Radius.Card)
        assertEquals("Avatar圆角应该等于Round", Tokens.Radius.Round, Tokens.Radius.Avatar)
    }

    // === 字体Token测试 ===

    @Test
    fun `字体Token应该有正确的层次结构`() {
        // 字体大小应该递增
        assertTrue("Tiny < Small", Tokens.Typography.Tiny < Tokens.Typography.Small)
        assertTrue("Small < Body", Tokens.Typography.Small < Tokens.Typography.Body)
        assertTrue("Body < BodyMedium", Tokens.Typography.Body < Tokens.Typography.BodyMedium)
        assertTrue("BodyMedium < BodyLarge", Tokens.Typography.BodyMedium < Tokens.Typography.BodyLarge)
        assertTrue("BodyLarge < Headline", Tokens.Typography.BodyLarge < Tokens.Typography.Headline)
        assertTrue("Headline < HeadlineLarge", Tokens.Typography.Headline < Tokens.Typography.HeadlineLarge)
        assertTrue("HeadlineLarge < Display", Tokens.Typography.HeadlineLarge < Tokens.Typography.Display)
        assertTrue("Display < DisplayLarge", Tokens.Typography.Display < Tokens.Typography.DisplayLarge)
    }

    @Test
    fun `字体Token应该使用合理的值`() {
        // 最小字体不应该太小
        assertTrue("Tiny字体应该>=10sp", Tokens.Typography.Tiny.value >= 10f)

        // 正文字体应该是14sp（标准阅读大小）
        assertEquals("Body字体应该是14sp", 14f, Tokens.Typography.Body.value, 0.1f)

        // 最大字体不应该过大
        assertTrue("DisplayLarge字体应该<=40sp", Tokens.Typography.DisplayLarge.value <= 40f)
    }

    // === 图标Token测试 ===

    @Test
    fun `图标Token应该有正确的层次结构`() {
        // 图标大小应该递增
        assertTrue("Tiny < XSmall", Tokens.Icon.Tiny < Tokens.Icon.XSmall)
        assertTrue("XSmall < Small", Tokens.Icon.XSmall < Tokens.Icon.Small)
        assertTrue("Small < Medium", Tokens.Icon.Small < Tokens.Icon.Medium)
        assertTrue("Medium < Standard", Tokens.Icon.Medium < Tokens.Icon.Standard)
        assertTrue("Standard < Large", Tokens.Icon.Standard < Tokens.Icon.Large)
        assertTrue("Large < XLarge", Tokens.Icon.Large < Tokens.Icon.XLarge)
        assertTrue("XLarge < XXLarge", Tokens.Icon.XLarge < Tokens.Icon.XXLarge)
    }

    @Test
    fun `图标Token应该使用标准的尺寸`() {
        // 标准图标应该是24dp（Material Design标准）
        assertEquals("Standard图标应该是24dp", 24.dp, Tokens.Icon.Standard)

        // 小图标应该是16dp
        assertEquals("Small图标应该是16dp", 16.dp, Tokens.Icon.Small)

        // 触摸目标应该是48dp（无障碍标准）
        assertEquals("TouchTarget应该是48dp", 48.dp, Tokens.Icon.TouchTarget)
    }

    // === 按钮Token测试 ===

    @Test
    fun `按钮Token应该使用合理的尺寸`() {
        // 按钮高度应该递增
        assertTrue("Compact < Small", Tokens.Button.HeightCompact < Tokens.Button.HeightSmall)
        assertTrue("Small < Secondary", Tokens.Button.HeightSmall < Tokens.Button.HeightSecondary)
        assertTrue("Secondary < Primary", Tokens.Button.HeightSecondary < Tokens.Button.HeightPrimary)
        assertTrue("Primary < Large", Tokens.Button.HeightPrimary < Tokens.Button.HeightLarge)

        // 最小宽度应该符合无障碍要求
        assertEquals("MinWidth应该是64dp", 64.dp, Tokens.Button.MinWidth)

        // 加载指示器应该合理
        assertTrue("LoadingIndicatorSize应该在合理范围", Tokens.Button.LoadingIndicatorSize.value in 16f..32f)
    }

    // === Token验证器测试 ===

    @Test
    fun `Token验证器应该正确识别禁用的硬编码值`() {
        val validator = GymBroTokenValidator

        // 测试禁用的颜色值
        assertTrue("应该识别硬编码黑色", validator.FORBIDDEN_HARDCODED_COLORS.contains("#000000"))
        assertTrue("应该识别硬编码白色", validator.FORBIDDEN_HARDCODED_COLORS.contains("#FFFFFF"))
        assertTrue("应该识别Color.Black", validator.FORBIDDEN_HARDCODED_COLORS.contains("Color.Black"))

        // 测试禁用的尺寸值
        assertTrue("应该识别硬编码8dp", validator.FORBIDDEN_HARDCODED_SIZES.contains("8.dp"))
        assertTrue("应该识别硬编码16dp", validator.FORBIDDEN_HARDCODED_SIZES.contains("16.dp"))
        assertTrue("应该识别硬编码24dp", validator.FORBIDDEN_HARDCODED_SIZES.contains("24.dp"))
    }

    @Test
    fun `Token验证器应该能够进行颜色对比度验证`() {
        val validator = GymBroTokenValidator

        // 测试高对比度组合（应该返回true）
        assertTrue(
            "黑白组合应该有足够对比度",
            validator.validateColorContrast(Color.Black, Color.White),
        )

        // 注：实际的对比度计算需要实现WCAG 2.1算法
        // 这里只是验证API存在
    }

    // === 工具函数 ===

    /**
     * 判断颜色是否为深色
     */
    private fun isDarkColor(color: Color): Boolean {
        val brightness = (color.red * 0.299f + color.green * 0.587f + color.blue * 0.114f)
        return brightness < 0.5f
    }

    /**
     * 判断颜色是否为浅色
     */
    private fun isLightColor(color: Color): Boolean {
        return !isDarkColor(color)
    }

    // === 集成测试 ===

    @Test
    fun `所有Token系统应该可以正常访问`() {
        // 验证Tokens对象包含所有子系统
        assertNotNull("Color系统应该存在", Tokens.Color)
        assertNotNull("Spacing系统应该存在", Tokens.Spacing)
        assertNotNull("Radius系统应该存在", Tokens.Radius)
        assertNotNull("Typography系统应该存在", Tokens.Typography)
        assertNotNull("Icon系统应该存在", Tokens.Icon)
        assertNotNull("Button系统应该存在", Tokens.Button)
        assertNotNull("Card系统应该存在", Tokens.Card)
        assertNotNull("Input系统应该存在", Tokens.Input)
        assertNotNull("ListItem系统应该存在", Tokens.ListItem)
        assertNotNull("AppBar系统应该存在", Tokens.AppBar)
        assertNotNull("Elevation系统应该存在", Tokens.Elevation)
        assertNotNull("Shadow系统应该存在", Tokens.Shadow)
        assertNotNull("Size系统应该存在", Tokens.Size)
        assertNotNull("Motion系统应该存在", Tokens.Motion)
        assertNotNull("Animation系统应该存在", Tokens.Animation)
    }

    // 🧹 REMOVED: 向后兼容性测试已删除，废弃Token已清理

    @Test
    fun `Progress颜色Token应该在不同主题下正确工作`() {
        // 验证Progress颜色系统
        assertNotNull("Progress.Idle应该存在", Tokens.Color.Progress.Idle)
        assertNotNull("Progress.Ongoing应该存在", Tokens.Color.Progress.Ongoing)
        assertNotNull("Progress.Done应该存在", Tokens.Color.Progress.Done)

        // 验证主题特定的Progress颜色
        assertNotNull("Progress.Light.Idle应该存在", Tokens.Color.Progress.Light.Idle)
        assertNotNull("Progress.Dark.Idle应该存在", Tokens.Color.Progress.Dark.Idle)

        // 验证它们不相同（适配不同主题）
        assertNotEquals(
            "浅色和深色主题的Idle颜色应该不同",
            Tokens.Color.Progress.Light.Idle,
            Tokens.Color.Progress.Dark.Idle,
        )
    }

    @Test
    fun `组件专用颜色Token应该正确定义`() {
        // 验证按钮颜色
        assertNotNull("ButtonPrimary应该存在", Tokens.Color.Component.ButtonPrimary)
        assertNotNull("ButtonSecondary应该存在", Tokens.Color.Component.ButtonSecondary)
        assertNotNull("ButtonDisabled应该存在", Tokens.Color.Component.ButtonDisabled)

        // 验证输入框颜色
        assertNotNull("InputBackground应该存在", Tokens.Color.Component.InputBackground)
        assertNotNull("InputBorder应该存在", Tokens.Color.Component.InputBorder)
        assertNotNull("InputBorderFocused应该存在", Tokens.Color.Component.InputBorderFocused)

        // 验证分割线颜色
        assertNotNull("DividerPrimary应该存在", Tokens.Color.Component.DividerPrimary)
        assertNotNull("DividerSecondary应该存在", Tokens.Color.Component.DividerSecondary)
    }
}
