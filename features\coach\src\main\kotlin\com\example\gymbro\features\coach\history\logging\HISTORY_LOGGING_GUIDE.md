# Coach History模块日志使用指南

## 📋 概述

本指南详细说明了Coach History子模块中日志系统的使用方法、标签规范和最佳实践。

🎯 **设计原则**: 简化日志标签，每个文件只保留1-3个核心标签，统一接入core的timber log进行动态管理。

## 🏗️ 架构集成

### 统一接入Core Timber Log
- **动态管理**: 通过TimberManager统一加载CoachLogTree
- **标签路由**: 通过ModuleAwareTree自动路由COA-HISTORY-*标签
- **配置管理**: 通过LoggingConfig统一配置History标签
- **优化处理**: 通过LogTagOptimizer自动优化标签显示

### 与Coach主模块的关系
- **继承体系**: History作为Coach的子模块，继承Coach的日志管理体系
- **标签前缀**: 使用COA-HISTORY-*前缀，明确标识子模块
- **统一处理**: 由CoachLogTree统一处理所有COA-*标签

## 🏷️ History标签规范（简化版）

### 核心标签（只保留3个）
```kotlin
COA-HISTORY          // 核心历史记录业务
COA-HISTORY-ERROR    // 错误处理
COA-HISTORY-DEBUG    // 调试信息
```

### 兼容旧标签
```kotlin
HISTORY-ACTOR        // → COA-HISTORY
```

## 🔧 使用方法

### 1. 基础日志使用
```kotlin
import timber.log.Timber
import com.example.gymbro.features.coach.history.logging.HistoryLogUtils

// 直接使用Timber + 标签
Timber.tag(HistoryLogUtils.TAG_CORE).i("历史记录加载开始")
Timber.tag(HistoryLogUtils.TAG_DEBUG).d("分页加载: page=${page}")
Timber.tag(HistoryLogUtils.TAG_ERROR).e("历史记录加载失败", exception)
```

### 2. 快速日志方法（简化版）
```kotlin
// 核心业务日志
HistoryLogUtils.Core.info("开始加载对话历史")
HistoryLogUtils.Core.debug("处理分页数据")
HistoryLogUtils.Core.error("历史记录处理失败", exception)
HistoryLogUtils.Core.warn("数据加载缓慢")
```

### 3. 统一流程跟踪（简化版）
```kotlin
// 历史记录流程跟踪
val sessionId = CompactIdGenerator.generateId("session")
HistoryLogUtils.logHistoryFlow("LOAD", "开始加载历史记录", sessionId)
HistoryLogUtils.logHistoryFlow("COMPLETE", "历史记录加载完成", sessionId)

// 错误跟踪
HistoryLogUtils.logHistoryError("LOAD", "加载失败", error.message, sessionId)
```

### 4. 性能监控
```kotlin
// 性能测量
HistoryLogUtils.PerformanceTracker.measureTime("历史记录加载") {
    loadHistoryData()
}

// 带返回值的性能测量
val result = HistoryLogUtils.PerformanceTracker.measureTimeWithResult("搜索历史记录") {
    searchHistory(query)
}

// 手动性能日志
Timber.tag(HistoryLogUtils.TAG_PERFORMANCE).i("⏱️ 历史记录渲染时间: ${duration}ms")
```

### 4. 通过CoachLogUtils快速访问（简化版）
```kotlin
// 使用Coach主模块的快速方法
CoachLogUtils.History.info("历史记录模块初始化")
CoachLogUtils.History.debug("处理历史数据")
CoachLogUtils.History.error("历史记录加载失败", exception)
CoachLogUtils.History.warn("历史记录数据不完整")
```

## 📊 日志级别规范

### 级别使用指南
- **ERROR**: 历史记录加载失败、数据库错误、搜索异常
- **WARN**: 分页加载超时、缓存失效、数据不一致
- **INFO**: 重要业务流程、分页加载成功、搜索结果
- **DEBUG**: 详细的流程跟踪、UI交互、数据映射
- **VERBOSE**: 高频操作（如滚动事件、实时搜索）

### 消息格式规范
```kotlin
// ✅ 好的日志格式
HistoryLogUtils.Core.info("🔥 [HISTORY-LOAD] 开始加载: sessionId=$sessionId, page=$page")
HistoryLogUtils.Paging.debug("📄 [PAGE-LOAD] 第${page}页加载完成: ${itemCount}项")

// ❌ 避免的格式
Timber.d("loading history") // 信息不足
Timber.i("分页完成") // 缺少具体信息
```

## 🎯 与Core Timber Log集成

### 自动路由机制
```kotlin
// 标签自动路由到Coach模块
tag?.startsWith("COA-HISTORY-") == true -> LoggingConfig.MODULE_COACH

// 通过CoachLogTree统一处理
private val HISTORY_TAGS = setOf(
    "COA-HISTORY-CORE", "COA-HISTORY-PAGING", "COA-HISTORY-DB", // ...
)
```

### 动态加载流程
```kotlin
// TimberManager自动加载CoachLogTree
loadCoachLogTree() // 包含History标签处理

// LoggingConfig统一配置
moduleConfigs[MODULE_COACH] = ModuleLogConfig(
    tags = setOf(
        "COA-HISTORY-CORE", "COA-HISTORY-PAGING", // ...
    )
)
```

### 标签优化处理
```kotlin
// LogTagOptimizer自动简化标签
.removePrefix("COA-HISTORY-") // COA-HISTORY-PAGING -> PAGING
```

## 📝 最佳实践

### 1. 标签选择原则
- **核心流程**: 使用 `COA-HISTORY-CORE`
- **分页操作**: 使用 `COA-HISTORY-PAGING`
- **数据库操作**: 使用 `COA-HISTORY-DB`
- **UI渲染**: 使用 `COA-HISTORY-UI`
- **搜索功能**: 使用 `COA-HISTORY-SEARCH`
- **错误处理**: 使用 `COA-HISTORY-ERROR`

### 2. 性能考虑
- 高频操作（如滚动）使用DEBUG级别
- 分页加载使用INFO级别记录关键节点
- 数据库操作记录执行时间和结果数量
- UI渲染记录组件名称和渲染项数

### 3. 错误处理规范
```kotlin
// ✅ 完整的错误日志
try {
    loadHistoryPage(page)
} catch (e: Exception) {
    HistoryLogUtils.logHistoryError("LOAD_PAGE", "分页加载失败", e.message ?: "未知错误", sessionId)
    throw e
}
```

## 🔗 相关文件

- `HistoryLogUtils.kt` - History专用日志工具类
- `CoachLogTree.kt` - Coach模块日志树（包含History标签处理）
- `CoachLogUtils.kt` - Coach主模块日志工具类（包含History快速方法）
- `core/logging/LoggingConfig.kt` - 全局日志配置
- `core/logging/TimberManager.kt` - 日志管理器

---

**更新日期**: 2025-01-26
**版本**: v1.0 (COA-HISTORY前缀标准)
**维护者**: GymBro开发团队
