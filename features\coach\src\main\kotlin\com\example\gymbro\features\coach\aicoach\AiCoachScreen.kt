package com.example.gymbro.features.coach.aicoach

// SummaryCard functionality is now integrated into the unified ThinkingBox component
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.example.gymbro.designSystem.components.GymBroToast
import com.example.gymbro.designSystem.components.ToastPosition
import com.example.gymbro.designSystem.components.ToastSeverity
import com.example.gymbro.designSystem.theme.coachTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.coach.aicoach.internal.components.AiCoachStrings
import com.example.gymbro.features.coach.aicoach.internal.components.ChatInterface
import com.example.gymbro.features.coach.aicoach.internal.components.ExpandedSuggestionPanel
import com.example.gymbro.features.coach.aicoach.internal.components.FunctionCallRenderer
import com.example.gymbro.features.coach.aicoach.internal.components.input.ChatGPTImagePickerWithLogic
import com.example.gymbro.features.coach.aicoach.internal.components.input.ChatInputContainer
import com.example.gymbro.features.coach.aicoach.internal.components.input.PortalToolsPanel
import com.example.gymbro.features.coach.aicoach.screen.AiCoachSideEffects
import com.example.gymbro.features.coach.aicoach.screen.AiCoachTopBar
import com.example.gymbro.features.coach.aicoach.screen.NetworkErrorEffect
import com.example.gymbro.features.coach.history.HistoryViewModel
import com.example.gymbro.features.coach.history.internal.components.ChatHistoryDrawer
import timber.log.Timber

// === UI层级常量定义 ===
private object AiCoachScreenZIndex {
    const val TopBar = 1000f // TopBar最高层级
    const val ImagePicker = 1001f // 图片选择器
    const val ToolsPanel = 999f // 工具面板
    const val SuggestionPanel = 15f // 建议面板
    const val InputContainer = 10f // 输入容器
    const val ChatContent = 1f // 聊天内容
}

// === 尺寸常量定义 === - 🧹 HARDCODE CLEANUP: 使用Token系统
private object AiCoachScreenSizes {
    val TopBarHeight = Tokens.AppBar.TopBarHeight // TopBar标准高度
    val InputContainerMinHeight = Tokens.Card.HeightSmall // 输入容器最小高度
    val InputContainerMaxHeight = Tokens.Card.HeightLarge // 输入容器最大高度
    val SuggestionPanelBottomPadding = Tokens.Card.HeightSmall // 建议面板底部间距
    val ImagePickerOffset = Tokens.Spacing.Massive.unaryMinus() // 图片选择器偏移，使用负间距
}

/**
 * AI Coach Screen - Unified ThinkingBox Integration
 *
 * This screen provides the main AI coaching interface with complete integration
 * of the unified ThinkingBox architecture for AI response rendering.
 *
 * Architecture:
 * - Scaffold layout management (TopBar + BottomBar + Content)
 * - Component assembly and state distribution
 * - Side effect handling coordination
 * - Navigation and history integration
 * - Unified ThinkingBox component integration
 *
 * @param navController Navigation controller
 * @param modifier Modifier parameter
 * @param viewModel AI Coach ViewModel
 * @param historyViewModel History ViewModel
 * @param actionId Quick action ID (optional)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AiCoachScreen(
    navController: NavController,
    modifier: Modifier = Modifier,
    viewModel: AiCoachViewModel = hiltViewModel(),
    historyViewModel: HistoryViewModel = hiltViewModel(),
    actionId: String? = null,
    exerciseLibraryNavigatable: com.example.gymbro.features.exerciselibrary.api.ExerciseLibraryNavigatable? = null,
) {
    // 🔥 Screen初始化
    Timber.tag("SCREEN-DEBUG").d("� AiCoachScreen渲染，ViewModel: @${viewModel.hashCode()}")

    // ==================== 状态管理 ====================

    val state by viewModel.state.collectAsStateWithLifecycle()
    var showPromptDebugPanel by remember { mutableStateOf(false) }

    // 🔥 【用户最佳阅读体验】画布重置和滑动控制状态管理
    var isCanvasLocked by remember { mutableStateOf(false) }
    var lastUserMessageCount by remember { mutableStateOf(0) }
    val chatListState = rememberLazyListState()

    // 🔥 【用户最佳阅读体验】监听用户消息变化，触发画布重置
    LaunchedEffect(state.messages.size) {
        val currentUserMessageCount = state.messages.count { it.isFromUser }

        // 检测到新的用户消息
        if (currentUserMessageCount > lastUserMessageCount) {
            lastUserMessageCount = currentUserMessageCount

            // 激活画布锁定状态
            isCanvasLocked = true
            Timber.d("🎯 [用户最佳阅读] 检测到新用户消息，激活画布重置: userMsgCount=$currentUserMessageCount")

            // 延迟重置画布到最佳视角（user message + thinkingcard）
            kotlinx.coroutines.delay(200) // 等待UI更新

            try {
                // 🔥 【关键修复】适配reverseLayout=true的滚动位置计算
                // 在反序布局中，index=0是最新内容，我们希望显示user message + thinking card
                val totalItems = chatListState.layoutInfo.totalItemsCount
                if (totalItems > 1) {
                    // 🔥 【反序布局修复】在反序布局中，较小的index更靠下（更新）
                    // 目标：显示最新的user message + thinking card在最佳位置
                    val targetIndex = when {
                        totalItems <= 3 -> 0 // 少量内容时滚动到最新位置
                        else -> minOf(1, totalItems - 1) // 适度向上滚动，确保both visible
                    }

                    chatListState.animateScrollToItem(
                        index = targetIndex,
                        scrollOffset = -Tokens.Spacing.Large.value.toInt(),
                    )
                    Timber.d("🎯 [用户最佳阅读] 反序画布重置完成: targetIndex=$targetIndex, totalItems=$totalItems")
                }
            } catch (e: Exception) {
                Timber.w(e, "🎯 [用户最佳阅读] 反序画布重置失败")
            }

            // 300ms后解锁画布，允许用户手动滑动
            kotlinx.coroutines.delay(300)
            isCanvasLocked = false
            Timber.d("🎯 [用户最佳阅读] 画布解锁，允许用户交互")
        }
    }

    // ==================== UI状态 ====================

    val drawerState = rememberDrawerState(DrawerValue.Closed)
    rememberCoroutineScope()
    val dispatch = remember(viewModel) { viewModel::dispatch }
    var showNetworkToast by remember { mutableStateOf(false) }
    var showSessionErrorToast by remember { mutableStateOf(false) } // 🔥 新增：会话错误Toast状态
    var sessionErrorMessage by remember { mutableStateOf("") } // 🔥 新增：会话错误消息

    // ==================== 副作用处理 ====================

    AiCoachSideEffects(
        state = state,
        effect = viewModel.effect,
        dispatch = dispatch,
        actionId = actionId,
    )

    NetworkErrorEffect(
        errorCode = state.errorCode,
        onNetworkError = { showNetworkToast = true },
        onErrorCleared = {
            showNetworkToast = false
            showSessionErrorToast = false // 🔥 清除会话错误状态
        },
        onSessionError = { message ->
            // 🔥 新增：会话错误处理
            sessionErrorMessage = message
            showSessionErrorToast = true
            Timber.tag("HISTORY-FIX").d("🔥 [Phase2] 显示会话错误Toast: $message")
        },
    )

    // ==================== 主界面布局 ====================

    // 🔥 清理：移除自动刷新，只在用户操作时刷新
    // 历史记录刷新现在完全由用户操作触发（发送消息、选择历史记录等）

    // 🔥 同步历史面板状态与抽屉状态
    LaunchedEffect(state.historyState.isVisible) {
        Timber.d("🔥 [UI-Sync] 历史面板状态变化: isVisible=${state.historyState.isVisible}")
        if (state.historyState.isVisible) {
            if (!drawerState.isOpen) {
                Timber.d("🔥 [UI-Sync] 打开历史记录抽屉")
                drawerState.open()
            }
        } else {
            if (drawerState.isOpen) {
                Timber.d("🔥 [UI-Sync] 关闭历史记录抽屉")
                drawerState.close()
            }
        }
    }

    // 🔥 监听抽屉状态变化，同步到历史面板状态
    LaunchedEffect(drawerState.targetValue) {
        val isDrawerOpen = drawerState.targetValue == DrawerValue.Open
        if (isDrawerOpen != state.historyState.isVisible) {
            Timber.d(
                "🔥 [UI-Sync] 抽屉状态变化: targetValue=${drawerState.targetValue}, 当前historyState.isVisible=${state.historyState.isVisible}",
            )
            // 只有当抽屉被手动关闭时才需要更新状态
            if (!isDrawerOpen && state.historyState.isVisible) {
                Timber.d("🔥 [UI-Sync] 用户手动关闭抽屉，更新历史面板状态")
                dispatch(AiCoachContract.Intent.ToggleHistoryPanel)
            }
        }
    }

    ChatHistoryDrawer(
        drawerState = drawerState,
        onNewChat = {
            Timber.d("🔥 [UI-NewChat] 用户点击新建对话按钮")
            dispatch(AiCoachContract.Intent.CreateNewSession)
            // 🔥 使用ToggleHistoryPanel意图关闭历史面板
            if (state.historyState.isVisible) {
                Timber.d("🔥 [UI-NewChat] 关闭历史记录面板")
                dispatch(AiCoachContract.Intent.ToggleHistoryPanel)
            }
        },
        onOpenConversation = { sessionId ->
            Timber.tag("HISTORY-FIX").d("🔄 [Phase3] UI层处理onOpenConversation: sessionId=$sessionId")
            Timber.d("🔥 [UI-OpenConversation] 用户选择会话: $sessionId")
            Timber.tag("HISTORY-FIX").d("🔄 [Phase3] 调用viewModel.switchToSession")
            viewModel.switchToSession(sessionId)
            // 🔥 使用ToggleHistoryPanel意图关闭历史面板
            if (state.historyState.isVisible) {
                Timber.tag("HISTORY-FIX").d("🔄 [Phase3] 关闭历史记录面板")
                Timber.d("🔥 [UI-OpenConversation] 关闭历史记录面板")
                dispatch(AiCoachContract.Intent.ToggleHistoryPanel)
            }
        },
        onSearch = { query ->
            historyViewModel.dispatch(
                com.example.gymbro.features.coach.history.HistoryContract.Intent.PerformSearch(
                    query = query,
                    mode = com.example.gymbro.features.coach.history.HistoryContract.SearchMode.HYBRID,
                ),
            )
        },
        viewModel = historyViewModel,
        // 🔥 新增：导航回调参数
        onNavigateToHome = {
            Timber.d("🔥 [Navigation] 导航到主页")
            navController.navigate("home_graph") {
                launchSingleTop = true
            }
        },
        onNavigateToExplore = {
            Timber.d("🔥 [Navigation] 导航到探索页面")
            // TODO: 实现探索页面导航
        },
        onNavigateToLibrary = {
            Timber.d("🔥 [Navigation] 导航到动作库")
            exerciseLibraryNavigatable?.let { navigatable ->
                navController.navigate(navigatable.route) {
                    launchSingleTop = true
                }
            }
        },
        onNavigateToSettings = {
            Timber.d("🔥 [Navigation] 导航到设置页面")
            navController.navigate("profile_graph") {
                launchSingleTop = true
            }
        },
        onNavigateToProfile = {
            Timber.d("🔥 [Navigation] 导航到个人信息页面")
            navController.navigate("profile_graph") {
                launchSingleTop = true
            }
        },
        modifier = modifier,
    ) {
        AiCoachScreenContent(
            state = state,
            dispatch = dispatch,
            viewModel = viewModel,
            navController = navController,
            drawerState = drawerState,
            showPromptDebugPanel = showPromptDebugPanel,
            onTogglePromptDebug = { showPromptDebugPanel = !showPromptDebugPanel },
            showNetworkToast = showNetworkToast,
            onDismissNetworkToast = { showNetworkToast = false },
            showSessionErrorToast = showSessionErrorToast, // 🔥 新增：传递会话错误状态
            sessionErrorMessage = sessionErrorMessage, // 🔥 新增：传递错误消息
            onDismissSessionErrorToast = { showSessionErrorToast = false }, // 🔥 新增：关闭回调
            promptRegistry = viewModel.getPromptRegistry(),
            onSwitchPromptMode = { viewModel.switchPromptMode(it) },
            onSwitchApiProvider = { provider ->
                viewModel.switchApiProvider(provider)
            },
            chatListState = chatListState, // 🔥 传递聊天列表状态
            isCanvasLocked = isCanvasLocked, // 🔥 传递画布锁定状态
        )
    }
}

/**
 * AI Coach Scaffold - 🚨 修复布局结构
 *
 * 负责 Scaffold 的布局管理，确保input在正确位置
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AiCoachScreenContent(
    state: AiCoachContract.State,
    dispatch: (AiCoachContract.Intent) -> Unit,
    viewModel: AiCoachViewModel,
    navController: NavController,
    drawerState: DrawerState,
    showPromptDebugPanel: Boolean,
    onTogglePromptDebug: () -> Unit,
    showNetworkToast: Boolean,
    onDismissNetworkToast: () -> Unit,
    showSessionErrorToast: Boolean, // 🔥 新增：会话错误Toast状态
    sessionErrorMessage: String, // 🔥 新增：会话错误消息
    onDismissSessionErrorToast: () -> Unit, // 🔥 新增：关闭会话错误Toast回调
    promptRegistry: com.example.gymbro.core.ai.prompt.registry.PromptRegistry,
    onSwitchPromptMode: (String) -> Unit,
    onSwitchApiProvider: (AiCoachContract.ApiProvider) -> Unit,
    chatListState: androidx.compose.foundation.lazy.LazyListState, // 🔥 新增：聊天列表状态
    isCanvasLocked: Boolean, // 🔥 新增：画布锁定状态
    modifier: Modifier = Modifier,
) {
    rememberCoroutineScope()
    val stableDispatch = remember(dispatch) { dispatch }

    // 🔥 新增: ModalBottomSheet 状态管理
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true, // 直接展开到底，体验更佳
    )
    val isSheetVisible = state.activeFunctionCall != null

    // 🔥 新增: 监听状态，自动展开/隐藏 BottomSheet
    LaunchedEffect(isSheetVisible) {
        if (isSheetVisible) {
            sheetState.show()
        } else {
            sheetState.hide()
        }
    }

    // 🔥 ModalBottomSheet 包装主内容
    if (isSheetVisible) {
        ModalBottomSheet(
            onDismissRequest = { stableDispatch(AiCoachContract.Intent.DismissFunctionCallResult) },
            sheetState = sheetState,
            containerColor = MaterialTheme.coachTheme.backgroundElevated,
            contentColor = MaterialTheme.coachTheme.textPrimary,
        ) {
            state.activeFunctionCall?.let { result ->
                FunctionCallRenderer(
                    result = result,
                    onDismiss = { stableDispatch(AiCoachContract.Intent.DismissFunctionCallResult) },
                    onAction = { prefillText ->
                        // 🔥 修复：检查是否有actionTriggered，如果有则触发导航
                        if (result.actionTriggered != null) {
                            Timber.d("🎯 Function Call结果触发导航: ${result.actionTriggered}")
                            when (result.actionTriggered) {
                                "CreateBlankPlan" -> stableDispatch(AiCoachContract.Intent.NavigateToPlans)
                                "GenerateTemplate" -> stableDispatch(
                                    AiCoachContract.Intent.NavigateToTemplates,
                                )
                                "CreateCustomExercise" -> stableDispatch(
                                    AiCoachContract.Intent.NavigateToExerciseLibrary,
                                )
                                "StartNewSession", "LoadFromTemplate" -> stableDispatch(
                                    AiCoachContract.Intent.NavigateToWorkout,
                                )
                                "AddSet", "CompleteWorkout" -> stableDispatch(
                                    AiCoachContract.Intent.NavigateToWorkout,
                                )
                                else -> {
                                    Timber.w("🎯 未知的actionTriggered: ${result.actionTriggered}，使用文本回填")
                                    stableDispatch(
                                        AiCoachContract.Intent.PrefillInputAndNavigate(prefillText),
                                    )
                                }
                            }
                        } else {
                            // 没有actionTriggered，使用默认的文本回填
                            stableDispatch(AiCoachContract.Intent.PrefillInputAndNavigate(prefillText))
                        }
                        stableDispatch(AiCoachContract.Intent.DismissFunctionCallResult)
                    },
                )
            }
        }
    }

    // 主聊天区域 - 🔥 【主题修复】使用Coach主题背景色，支持主题切换
    Box(
        modifier =
        modifier
            .fillMaxSize()
            .background(MaterialTheme.coachTheme.backgroundPrimary), // 🔥 【主题修复】使用Coach主题背景色，支持主题切换
    ) {
        // 顶部栏 - 🔥 【TopBar交互修复】添加高优先级zIndex确保始终可点击
        AiCoachTopBar(
            state = state,
            bgeEngineStatus = viewModel.bgeEngineStatus.collectAsState(),
            onBackClick = {
                // 🔥 【导航修复】直接导航到Home模块而不是popBackStack，解决返回按钮无法回到Home的问题
                Timber.d("🔥 [Navigation-Fix] AI Coach返回按钮点击，导航到Home模块")
                navController.navigate("home_graph") {
                    launchSingleTop = true
                }
            },
            onHistoryClick = {
                // 🔥 修改：切换历史记录面板而不是打开抽屉
                stableDispatch(AiCoachContract.Intent.ToggleHistoryPanel)
            },
            onNewChatClick = {
                Timber.d("🔥 [UI-TopBar] 用户点击顶部栏新建对话按钮")
                stableDispatch(AiCoachContract.Intent.CreateNewSession)
            },
            onClearSessionClick = { stableDispatch(AiCoachContract.Intent.ClearCurrentSession) },
            onTogglePromptDebug = onTogglePromptDebug,
            showPromptDebugPanel = showPromptDebugPanel,
            promptRegistry = promptRegistry,
            onSwitchPromptMode = onSwitchPromptMode,
            onSwitchApiProvider = onSwitchApiProvider,
            // 🔥 新增：调试面板参数
            showDebugPanel = showPromptDebugPanel, // 复用现有的调试面板状态
            onDebugToggle = { _ -> onTogglePromptDebug() }, // 适配参数类型
            onDebugPageClick = {
                // TODO: 导航到 CoachDebugScreen
                Timber.d("🔥 [Debug] 点击调试页面按钮")
            },
            // 🔥 激活Debug功能：集成真实Function Call调试
            debugScenarios = com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.allMockScenarios,
            onDebugScenarioSelected = { scenario ->
                Timber.d("🔥 [Debug] 触发模拟Function Call: ${scenario.displayName}")
                stableDispatch(AiCoachContract.Intent.DebugTriggerFunctionCallResult(scenario.result))
            },
            // 🔥 新增：真实执行调试功能
            realExecutionScenarios = com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.allRealExecutionScenarios,
            onRealExecutionSelected = { scenario ->
                Timber.d("🔥 [Debug] 执行真实Function Call: ${scenario.displayName}")
                stableDispatch(AiCoachContract.Intent.DebugExecuteRealFunctionCall(scenario.functionName))
            },
            modifier =
            Modifier
                .align(Alignment.TopCenter)
                .fillMaxWidth()
                .statusBarsPadding()
                .zIndex(AiCoachScreenZIndex.TopBar), // 🔥 【关键修复】确保TopBar始终在最上层，不被其他组件覆盖
        )

        // 🔥 【顶部空白修复】聊天内容区域 - 移除重复的状态栏padding
        ChatContentArea(
            state = state,
            dispatch = dispatch,
            viewModel = viewModel,
            chatListState = chatListState,
            isCanvasLocked = isCanvasLocked,
            modifier =
            Modifier
                // 🔥 【触摸事件修复】先设置padding再fillMaxSize，确保触摸区域从TopBar下方开始
                .padding(
                    top = AiCoachScreenSizes.TopBarHeight, // TopBar标准高度
                    bottom = 0.dp, // 移除固定bottom padding，让ChatInputContainer自己处理
                ).fillMaxSize() // 🔥 【关键修复】调整顺序，避免LazyColumn拦截TopBar的触摸事件
                .zIndex(AiCoachScreenZIndex.ChatContent), // 🔥 【TopBar交互修复】设置较低zIndex，确保不覆盖TopBar
        )

        // 🔥 【架构修复】扩展建议面板 - 独立组件，与输入框平级
        ExpandedSuggestionPanel(
            isVisible = state.suggestionConfig != null, // 🔥 修复：基于suggestionConfig判断是否显示
            inputText = state.inputState.text, // 🔥 修复：使用正确的字段名
            suggestionConfig = state.suggestionConfig,
            onSuggestionClick = { suggestion ->
                stableDispatch(AiCoachContract.Intent.SendMessage(suggestion))
            },
            onDismiss = {
                // 🔥 修复：暂时使用空实现，后续可以添加专门的Intent
                // TODO: 添加ClearSuggestions Intent
            },
            modifier =
            Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .padding(bottom = AiCoachScreenSizes.SuggestionPanelBottomPadding) // 🔥 在输入框上方显示，留出输入框空间
                .zIndex(AiCoachScreenZIndex.SuggestionPanel),
            // 🔥 确保显示在输入框上方
        )

        // 🎯 输入框容器 - 从底部开始，内容自适应高度
        ChatInputContainer(
            state = state,
            onIntent = stableDispatch,
            modifier =
            Modifier
                .align(Alignment.BottomCenter) // 🔥 底部居中对齐
                .fillMaxWidth() // 🔥 横向填满屏幕
                .wrapContentHeight() // 🔥 根据内容自适应高度
                .imePadding() // 🔥 在Screen级别处理键盘适配
                .heightIn(
                    min = AiCoachScreenSizes.InputContainerMinHeight, // 🔥 修复：为ActionToolbar留出足够空间，避免input被压扁
                    max = AiCoachScreenSizes.InputContainerMaxHeight, // 🔥 修复：增加最大高度，避免有内容时被压缩
                ).zIndex(AiCoachScreenZIndex.InputContainer), // 🔥 设置层级，低于扩展面板
        )

        // Toast提示
        if (showNetworkToast) {
            GymBroToast(
                message = AiCoachStrings.networkError,
                severity = ToastSeverity.ERROR,
                position = ToastPosition.TOP_CENTER,
                isRetryable = true,
                actionLabel = AiCoachStrings.retry,
                onAction = onDismissNetworkToast,
                onDismiss = onDismissNetworkToast,
                modifier = Modifier.align(Alignment.TopCenter),
            )
        }

        // � 新增：会话错误Toast
        if (showSessionErrorToast) {
            GymBroToast(
                message =
                com.example.gymbro.core.ui.text.UiText
                    .DynamicString(sessionErrorMessage),
                severity = ToastSeverity.ERROR,
                position = ToastPosition.TOP_CENTER,
                isRetryable = true,
                actionLabel = AiCoachStrings.retry,
                onAction = onDismissSessionErrorToast,
                onDismiss = onDismissSessionErrorToast,
                modifier = Modifier.align(Alignment.TopCenter),
            )
        }

        // �🚀 Portal工具面板 - 🔥 【TopBar交互修复】调整层级，不覆盖TopBar
        PortalToolsPanel(
            isVisible = state.inputState.isToolbarExpanded,
            tools = state.suggestionConfig?.quickSuggestions ?: emptyList(), // 使用状态中的快速建议
            onToolClick = { functionCallName ->
                stableDispatch(AiCoachContract.Intent.OnToolSelected(functionCallName))
                // 移除OnToolsClick调用，OnToolSelected已经会关闭面板
            },
            onDismiss = {
                stableDispatch(AiCoachContract.Intent.OnToolsClick)
            },
            modifier =
            Modifier
                .fillMaxSize()
                .zIndex(AiCoachScreenZIndex.ToolsPanel),
            // 🔥 【TopBar交互修复】设置高层级但低于TopBar，确保工具面板可见但不阻挡TopBar
        )

        // 🔥 修复：图片选择器面板 - Screen级别，不受容器高度限制
        if (state.isImagePickerVisible) {
            Box(
                modifier =
                Modifier
                    .fillMaxSize()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null,
                    ) {
                        // 🔥 点击外部关闭图片选择器
                        stableDispatch(AiCoachContract.Intent.HideImagePicker)
                    },
            ) {
                ChatGPTImagePickerWithLogic(
                    isVisible = true,
                    onDismiss = { stableDispatch(AiCoachContract.Intent.HideImagePicker) },
                    onImagesSelected = { uris ->
                        stableDispatch(AiCoachContract.Intent.OnImagesSelected(uris))
                        stableDispatch(AiCoachContract.Intent.HideImagePicker) // 选择后自动关闭
                    },
                    modifier =
                    Modifier
                        .align(Alignment.BottomStart) // 🔥 基于Screen底部左侧定位
                        .offset(
                            x = Tokens.Spacing.Medium, // 🔥 与输入框左边距对齐
                            y = AiCoachScreenSizes.ImagePickerOffset, // 🔥 在输入框上方，完全脱离容器范围
                        ).zIndex(AiCoachScreenZIndex.ImagePicker) // 🔥 比Portal面板更高的层级
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null,
                        ) { /* 🔥 阻止事件冒泡 */ },
                )
            }
        }

        // 🔥 Function Call 结果现在通过 ModalBottomSheet 显示，不再需要 Dialog
    } // Box 结束

    // SummaryCard functionality is now integrated into the unified ThinkingBox component
    // All AI response summary and thinking process display is handled centrally within ThinkingBox
    // This ensures complete module encapsulation and single export point architecture
}

/**
 * AI Coach 主内容区域
 *
 * 🔥 修复：移除BGE加载状态检测，聊天界面始终显示
 * BGE由统一管理器后台处理，不阻塞UI体验
 */
@Composable
private fun AiCoachMainContent(
    state: AiCoachContract.State,
    dispatch: (AiCoachContract.Intent) -> Unit,
    viewModel: AiCoachViewModel,
    showNetworkToast: Boolean,
    onDismissNetworkToast: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        // 🔥 直接显示聊天界面，不再检查BGE引擎状态
        ChatContentArea(
            state = state,
            dispatch = dispatch,
            viewModel = viewModel,
            modifier = Modifier.fillMaxSize(),
        )

        // 网络错误Toast
        if (showNetworkToast) {
            GymBroToast(
                message = AiCoachStrings.networkError,
                severity = ToastSeverity.ERROR,
                position = ToastPosition.TOP_CENTER,
                isRetryable = true,
                actionLabel = AiCoachStrings.retry,
                onAction = onDismissNetworkToast,
                onDismiss = onDismissNetworkToast,
                modifier = Modifier.align(Alignment.TopCenter),
            )
        }
    }
}

/**
 * 聊天内容区域
 *
 * 简化的聊天界面，主要职责：
 * - 集成 ChatInterface 组件
 * - 处理 Function Call 结果展示
 * - 管理聊天界面的动画切换
 */

@Composable
private fun ChatContentArea(
    state: AiCoachContract.State,
    dispatch: (AiCoachContract.Intent) -> Unit,
    viewModel: AiCoachViewModel,
    chatListState: androidx.compose.foundation.lazy.LazyListState? = null,
    isCanvasLocked: Boolean = false,
    modifier: Modifier = Modifier,
) {
    // 🔥 【P0修复】确认单一实例
    Timber
        .tag("UI-DEBUG")
        .d("🔍 [P0修复] ChatContentArea渲染 - 确保单一ChatInterface实例")

    Box(modifier = modifier) {
        // 主聊天界面 - 唯一实例
        ChatInterface(
            state = state,
            onIntent = dispatch,
            viewModel = viewModel,
            modifier = Modifier.fillMaxSize(),
        )

        // Function Call 结果展示 - 暂时注释，等待实现
        // TODO: 实现 Function Call 结果展示功能
    }
}

// ==================== ThinkingBox模块集成完成总结 ====================
//
// 🎯 【集成状态】ThinkingBox模块完全集成 ✅
//
// 1. 依赖配置：
//    ✅ build.gradle.kts: 已添加 implementation(project(":features:thinkingbox"))
//    ✅ ThinkingBoxModule: 已配置 CoroutineScope 依赖注入
//    ✅ ThinkingBox: 已使用 @ThinkingBoxScope 限定符
//
// 2. 组件导入和使用：
//    ✅ ChatInterface.kt: 已导入所有thinkingbox模块组件
//    ✅ 主要实现: ThinkingCard（流式架构）
//    ✅ 备选方案: AIThinkingMarkdownRenderer、ImprovedThinkingCard
//    ✅ 辅助函数: buildAIThinkingMarkdown（智能内容格式化）
//
// 3. 架构特性集成：
//    ✅ 令牌桶缓冲机制: Channel<String>(UNLIMITED) 高频token缓冲
//    ✅ 固定节拍器: 100ms间隔批次消费，平滑UI更新
//    ✅ 简约灰阶设计: Gray500/600/700统一色彩系统，移除表情符号
//    ✅ 智能内容解析: 思考阶段、关键洞察、推理步骤、不确定性指示器
//
// 4. 设计规范遵循：
//    ✅ Clean Architecture + MVI 2.0: 严格遵循项目架构模式
//    ✅ designSystem主题令牌: 使用统一的设计系统tokens
//    ✅ BUILD SUCCESSFUL标准: 所有变更都通过编译验证
//
// 5. 性能优化：
//    ✅ 令牌桶架构: 高性能、轻量级渲染、优化的动画效果
//    ✅ 状态流响应式: StateFlow<String> 响应式UI输出
//    ✅ 累积构建: StringBuilder 增量构建markdown
//
// 🚀 【集成效果】
// - 用户体验: 更流畅的思考过程展示，简约优雅的视觉设计
// - 开发体验: 统一的ThinkingBox组件API，易于维护和扩展
// - 性能提升: 令牌桶缓冲机制显著提升渲染性能
// - 架构一致性: 完全符合GymBro项目的架构标准和设计理念
//
// ==================== 原有架构重构说明 ====================
//
// 功能内聚架构重构完成：
//
// 1. Contract 拆分：
//    - contract/AiCoachState.kt: 状态定义和数据模型
//    - contract/AiCoachIntent.kt: 所有用户意图
//    - contract/AiCoachEffect.kt: 所有副作用
//    - contract/AiCoachExtensions.kt: 扩展函数和工具方法
//

// 🔥 Function Call 结果现在通过 ModalBottomSheet + FunctionCallRenderer 显示

// ThinkingBox integration completed - all AI response functionality
// is now handled through the unified ThinkingBox component in ChatInterface.kt
