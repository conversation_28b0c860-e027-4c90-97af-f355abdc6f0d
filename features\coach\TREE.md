# Coach Module Structure

**Version:** 5.0 - 日志系统统一化架构
**Last Updated:** 2025-01-26
**Status:** ✅ Single Data Source + Clean Architecture + MVI 2.0 + 统一日志系统

## 📁 Module Tree Structure

```
features/coach/
├── 📄 README.md                    # 模块概览和架构文档
├── 📄 HISTORY_README.md            # 历史保存系统文档
├── 📄 TREE.md                      # 本文件 - 模块结构说明
├── 📄 build.gradle.kts             # 模块构建配置
│
├── 📁 docs/                        # 文档目录
│   ├── 📄 workflow.md              # 工作流程和修复记录
│   ├── 📄 705多轮对话解决方案.md    # 多轮对话架构设计
│   └── 📁 history/                 # 历史系统相关文档
│       └── 📄 README.md            # 历史系统详细文档
│
├── 📁 src/main/kotlin/com/example/gymbro/features/coach/
│   │
│   ├── 📁 logging/                 # 日志系统 (v5.0新增)
│   │   ├── 📄 CoachLogTree.kt      # Coach专用日志树
│   │   ├── 📄 CoachLogUtils.kt     # Coach日志工具类
│   │   └── 📄 COACH_LOGGING_GUIDE.md # Coach日志使用指南
│   │
│   ├── 📁 aicoach/                 # AI教练主功能
│   │   ├── 📄 AiCoachContract.kt   # MVI契约定义 (单一数据源架构)
│   │   ├── 📄 AiCoachViewModel.kt  # 主ViewModel (统一Effect处理)
│   │   ├── 📄 AiCoachScreen.kt     # 主UI界面
│   │   │
│   │   └── 📁 internal/            # 内部实现
│   │       ├── 📁 reducer/         # Reducer层
│   │       │   ├── 📄 AiCoachReducer.kt           # 主Reducer (统一ID生成)
│   │       │   └── 📄 MessagingReducerHandler.kt  # 消息处理Reducer
│   │       │
│   │       ├── 📁 effect/          # Effect处理层
│   │       │   └── 📄 AiCoachEffectHandler.kt     # Effect处理器
│   │       │
│   │       ├── 📁 viewmodel/       # ViewModel辅助层
│   │       │   └── 📄 AiCoachSessionHandler.kt    # 会话处理器 (唯一保存入口)
│   │       │
│   │       └── 📁 ui/              # UI组件
│   │           ├── 📄 MessageCard.kt              # 消息卡片
│   │           ├── 📄 ChatInput.kt                # 输入组件
│   │           └── 📄 ThinkingBoxIntegration.kt   # ThinkingBox集成
│   │
│   ├── 📁 history/                 # 历史记录功能
│   │   ├── 📄 HistoryScreen.kt     # 历史记录界面
│   │   ├── 📄 HistoryViewModel.kt  # 历史记录ViewModel
│   │   ├── 📁 logging/             # History专用日志系统 (v5.0新增)
│   │   │   ├── 📄 HistoryLogUtils.kt # History日志工具类
│   │   │   └── 📄 HISTORY_LOGGING_GUIDE.md # History日志使用指南
│   │   └── 📁 internal/            # 历史记录内部实现
│   │       ├── 📄 ConversationPagingSource.kt    # 分页数据源
│   │       └── 📄 HistoryRepository.kt            # 历史记录Repository
│   │
│   └── 📁 shared/                  # 共享组件
│       ├── 📄 CoachNavigation.kt   # 导航定义
│       ├── 📄 CoachTheme.kt        # 主题定义
│       └── 📁 components/          # 共享UI组件
│           ├── 📄 ChatBubble.kt    # 聊天气泡
│           └── 📄 LoadingIndicator.kt # 加载指示器
│
└── 📁 src/test/                    # 测试目录
    ├── 📁 kotlin/                  # Kotlin单元测试
    └── 📁 resources/               # 测试资源
```

## 🎯 核心架构组件 (Single Data Source + 统一日志系统)

### 📊 日志系统架构 (v5.0新增)

#### **日志组件结构**:

```
CoachLogTree (专用日志树)
├── COA-CORE        # 核心业务日志
├── COA-ERROR       # 错误处理日志
└── COA-DEBUG       # 调试信息日志

HistoryLogUtils (History子模块)
├── COA-HISTORY         # History核心业务
├── COA-HISTORY-ERROR   # History错误处理
└── COA-HISTORY-DEBUG   # History调试信息
```

#### **日志系统特性**:

- ✅ **简化标签体系**: 每个文件只保留1-3个核心标签
- ✅ **统一接入**: 通过core的timber log动态管理
- ✅ **CompactIdGenerator集成**: 使用压缩ID提高日志可读性
- ✅ **模块隔离**: Coach和History有独立的日志标签
- ✅ **向后兼容**: 支持旧COACH-*标签逐步迁移

### 📊 数据流架构

#### **唯一保存路径**:

```
UI Input → AiCoachViewModel → ChatSessionManagementUseCase → ChatSessionRepositoryImpl → ChatRawDao → chat_raw表
```

#### **关键组件职责**:

1. **AiCoachContract.kt** - MVI契约定义
    - Intent: 统一的用户意图定义
    - State: UI状态定义
    - Effect: 单一数据源的副作用定义

2. **AiCoachViewModel.kt** - 主控制器
    - MVI循环管理
    - 统一的Effect处理
    - ThinkingBox集成协调

3. **AiCoachReducer.kt** - 状态管理
    - Intent → State转换
    - Effect生成
    - 统一的消息ID生成 (`Constants.MessageId.generate()`)

4. **AiCoachSessionHandler.kt** - 会话处理
    - **唯一的消息保存入口**
    - ChatRaw架构集成
    - ThinkingBox内容保存到专用字段

5. **ChatSessionRepositoryImpl** (data层) - 数据持久化
    - **唯一的数据库写入点**
    - ChatRaw表操作
    - 事务安全保证

6. **CoachLogTree.kt** (日志系统) - 统一日志管理
    - Coach模块专用日志树
    - 处理所有COA-*标签
    - 支持日志聚合和过滤

7. **HistoryLogUtils.kt** (History日志) - History专用日志
    - History子模块日志工具
    - 简化的3标签体系
    - 统一接入core timber log

### 🔧 关键特性

#### **单一数据源原则**:

- ✅ ChatRaw表作为唯一权威数据源
- ✅ 统一的消息保存路径
- ✅ 消除双重写入问题
- ❌ MessageEvent表写入已禁用
- ❌ HistoryPersister实际写入已禁用

#### **ThinkingBox集成**:

- ✅ 思考内容保存到ChatRaw表的`thinkingNodes`字段
- ✅ 最终结果保存到`finalMarkdown`字段
- ✅ 完整的思考过程记录

#### **多轮对话支持**:

- ✅ 消息关联通过`in_reply_to_message_id`字段
- ✅ 会话隔离通过`session_id`字段
- ✅ 完整的对话上下文保持

## 📚 相关文档

- [README.md](./README.md) - 模块概览和技术架构
- [HISTORY_README.md](./HISTORY_README.md) - 历史保存系统详细文档
- [docs/workflow.md](./docs/workflow.md) - 工作流程和修复记录

## 🔄 版本历史

- **v5.0** (2025-01-26): 日志系统统一化架构 - CoachLogTree + 简化标签体系 + CompactIdGenerator集成
- **v4.0** (2025-07-07): Single Data Source Architecture - 单一数据源架构重构
- **v3.0** (2025-07-06): Multi-Turn Conversation Architecture - 多轮对话架构
- **v2.0** (2025-07-03): ROOM单一数据源模式 - 移除AutoSave双重写入
- **v1.0** (2025-06-xx): 初始版本 - 基础AI教练功能
