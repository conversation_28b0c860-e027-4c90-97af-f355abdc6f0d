package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ThinkingBoxReducer测试 - Plan B重构版本
 * 
 * 🔥 【Plan B重构】验证主Reducer逻辑，确保sessionId移除后的功能完整性
 */
class ThinkingBoxReducerTest {

    private lateinit var reducer: ThinkingBoxReducer

    @BeforeEach
    fun setup() {
        reducer = ThinkingBoxReducer()
    }

    @Test
    @DisplayName("【初始化】Initialize Intent应该正确处理")
    fun `should handle Initialize intent correctly`() {
        // Given
        val intent = ThinkingBoxContract.Intent.Initialize("test-message-123")
        val currentState = ThinkingBoxContract.State()
        
        // When
        val result = reducer.reduce(intent, currentState)
        
        // Then
        assertEquals("test-message-123", result.state.messageId)
        // 🔥 【Plan B重构】不再验证sessionId，通过ConversationIdManager获取
        assertFalse(result.state.isLoading)
        
        // 验证Effect
        assertEquals(1, result.effects.size)
        assertTrue(result.effects[0] is ThinkingBoxContract.Effect.StartTokenStreamListening)
        val effect = result.effects[0] as ThinkingBoxContract.Effect.StartTokenStreamListening
        assertEquals("test-message-123", effect.messageId)
    }

    @Test
    @DisplayName("【重置】Reset Intent应该正确处理")
    fun `should handle Reset intent correctly`() {
        // Given
        val intent = ThinkingBoxContract.Intent.Reset
        val currentState = ThinkingBoxContract.State(
            messageId = "existing-message",
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-1",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.THINKING,
                    title = "思考",
                    content = "内容",
                    isComplete = true,
                    isRendered = false
                )
            ),
            finalReady = true,
            finalContent = "最终内容",
            thinkingClosed = true
        )
        
        // When
        val result = reducer.reduce(intent, currentState)
        
        // Then
        assertEquals("", result.state.messageId)
        assertTrue(result.state.segmentsQueue.isEmpty())
        assertFalse(result.state.finalReady)
        assertEquals("", result.state.finalContent)
        assertFalse(result.state.thinkingClosed)
        assertFalse(result.state.isLoading)
        assertEquals(null, result.state.error)
    }

    @Test
    @DisplayName("【UI回调】UiSegmentRendered Intent应该正确处理")
    fun `should handle UiSegmentRendered intent correctly`() {
        // Given
        val intent = ThinkingBoxContract.Intent.UiSegmentRendered("segment-123")
        val currentState = ThinkingBoxContract.State(
            messageId = "test-message",
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-123",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.THINKING,
                    title = "思考",
                    content = "内容",
                    isComplete = true,
                    isRendered = false
                )
            )
        )
        
        // When
        val result = reducer.reduce(intent, currentState)
        
        // Then
        assertEquals(1, result.state.segmentsQueue.size)
        val updatedSegment = result.state.segmentsQueue[0]
        assertEquals("segment-123", updatedSegment.id)
        assertTrue(updatedSegment.isRendered)
        
        // 验证其他属性保持不变
        assertEquals("思考", updatedSegment.title)
        assertEquals("内容", updatedSegment.content)
        assertTrue(updatedSegment.isComplete)
    }

    @Test
    @DisplayName("【UI回调】UiSegmentRendered对不存在的段应该不影响状态")
    fun `should not affect state when UiSegmentRendered for non-existent segment`() {
        // Given
        val intent = ThinkingBoxContract.Intent.UiSegmentRendered("non-existent-segment")
        val currentState = ThinkingBoxContract.State(
            messageId = "test-message",
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-123",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.THINKING,
                    title = "思考",
                    content = "内容",
                    isComplete = true,
                    isRendered = false
                )
            )
        )
        
        // When
        val result = reducer.reduce(intent, currentState)
        
        // Then
        assertEquals(currentState, result.state)
        assertTrue(result.effects.isEmpty())
    }

    @Test
    @DisplayName("【错误处理】ClearError Intent应该正确处理")
    fun `should handle ClearError intent correctly`() {
        // Given
        val intent = ThinkingBoxContract.Intent.ClearError
        val currentState = ThinkingBoxContract.State(
            messageId = "test-message",
            error = com.example.gymbro.core.ui.text.UiText.DynamicString("测试错误")
        )
        
        // When
        val result = reducer.reduce(intent, currentState)
        
        // Then
        assertEquals(null, result.state.error)
        assertEquals("test-message", result.state.messageId) // 其他状态保持不变
    }

    @Test
    @DisplayName("【状态转换】convertToContractState应该正确转换")
    fun `should convert TBState to ContractState correctly`() {
        // Given
        val tbState = SegmentQueueReducer.TBState(
            messageId = "test-message-456",
            // 🔥 【Plan B重构】不再包含sessionId
            thinkingClosed = true,
            finalBuffer = StringBuilder("最终答案内容"),
            queue = java.util.ArrayDeque<com.example.gymbro.features.thinkingbox.domain.model.Segment>().apply {
                add(com.example.gymbro.features.thinkingbox.domain.model.Segment(
                    id = "segment-1",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.THINKING,
                    title = "思考阶段",
                    content = "思考内容",
                    closed = true
                ))
            }
        )
        
        // When
        val contractState = reducer.convertToContractState(tbState)
        
        // Then
        assertEquals("test-message-456", contractState.messageId)
        // 🔥 【Plan B重构】不再验证sessionId
        assertEquals(1, contractState.segmentsQueue.size)
        assertTrue(contractState.finalReady)
        assertEquals("最终答案内容", contractState.finalContent)
        assertTrue(contractState.thinkingClosed)
        assertFalse(contractState.isLoading)
        
        // 验证SegmentUi转换
        val segmentUi = contractState.segmentsQueue[0]
        assertEquals("segment-1", segmentUi.id)
        assertEquals(com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.THINKING, segmentUi.kind)
        assertEquals("思考阶段", segmentUi.title)
        assertEquals("思考内容", segmentUi.content)
        assertTrue(segmentUi.isComplete)
        assertFalse(segmentUi.isRendered) // 默认未渲染
    }

    @Test
    @DisplayName("【状态转换】空队列的convertToContractState应该正确处理")
    fun `should convert empty TBState to ContractState correctly`() {
        // Given
        val tbState = SegmentQueueReducer.TBState(
            messageId = "empty-test",
            thinkingClosed = false,
            finalBuffer = StringBuilder(),
            queue = java.util.ArrayDeque()
        )
        
        // When
        val contractState = reducer.convertToContractState(tbState)
        
        // Then
        assertEquals("empty-test", contractState.messageId)
        assertTrue(contractState.segmentsQueue.isEmpty())
        assertFalse(contractState.finalReady)
        assertEquals("", contractState.finalContent)
        assertFalse(contractState.thinkingClosed)
        assertFalse(contractState.isLoading)
    }

    @Test
    @DisplayName("【状态转换】包含current段的convertToContractState应该正确处理")
    fun `should convert TBState with current segment to ContractState correctly`() {
        // Given
        val currentSegment = com.example.gymbro.features.thinkingbox.domain.model.Segment(
            id = "current-segment",
            kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.PHASE,
            title = "当前阶段",
            content = "当前内容",
            closed = false
        )
        val tbState = SegmentQueueReducer.TBState(
            messageId = "current-test",
            current = currentSegment,
            queue = java.util.ArrayDeque()
        )
        
        // When
        val contractState = reducer.convertToContractState(tbState)
        
        // Then
        assertEquals("current-test", contractState.messageId)
        assertEquals(1, contractState.segmentsQueue.size)
        
        val segmentUi = contractState.segmentsQueue[0]
        assertEquals("current-segment", segmentUi.id)
        assertEquals(com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.PHASE, segmentUi.kind)
        assertEquals("当前阶段", segmentUi.title)
        assertEquals("当前内容", segmentUi.content)
        assertFalse(segmentUi.isComplete) // current段未完成
        assertFalse(segmentUi.isRendered)
    }

    @Test
    @DisplayName("【状态转换】混合current和queue的convertToContractState应该正确处理")
    fun `should convert TBState with both current and queue to ContractState correctly`() {
        // Given
        val currentSegment = com.example.gymbro.features.thinkingbox.domain.model.Segment(
            id = "current-segment",
            kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.THINKING,
            title = "当前思考",
            content = "当前内容",
            closed = false
        )
        val queueSegment = com.example.gymbro.features.thinkingbox.domain.model.Segment(
            id = "queue-segment",
            kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.PHASE,
            title = "队列阶段",
            content = "队列内容",
            closed = true
        )
        val tbState = SegmentQueueReducer.TBState(
            messageId = "mixed-test",
            current = currentSegment,
            queue = java.util.ArrayDeque<com.example.gymbro.features.thinkingbox.domain.model.Segment>().apply {
                add(queueSegment)
            }
        )
        
        // When
        val contractState = reducer.convertToContractState(tbState)
        
        // Then
        assertEquals("mixed-test", contractState.messageId)
        assertEquals(2, contractState.segmentsQueue.size)
        
        // 验证current段在前
        val currentUi = contractState.segmentsQueue[0]
        assertEquals("current-segment", currentUi.id)
        assertFalse(currentUi.isComplete)
        
        // 验证queue段在后
        val queueUi = contractState.segmentsQueue[1]
        assertEquals("queue-segment", queueUi.id)
        assertTrue(queueUi.isComplete)
    }
}
