package com.example.gymbro.features.coach.aicoach.internal.reducer.handlers

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.conversation.createMessageContextWithLogging
import com.example.gymbro.core.conversation.getSessionContextSafely
import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import kotlinx.collections.immutable.toImmutableList
import kotlinx.datetime.Clock
import timber.log.Timber
import javax.inject.Inject

/**
 * MessagingReducerHandler - 处理所有与消息和输入相关的Intent
 *
 * 🔥 【Plan B重构】核心变更：
 * - 集成ConversationIdManager统一ID管理
 * - 简化ID传递逻辑，消除conversationId概念
 * - 增强容错机制和智能ID匹配
 * - 使用CompactIdGenerator优化日志显示
 *
 * 负责：
 * 1. 消息发送和接收（使用统一ID管理）
 * 2. 输入状态管理
 * 3. 附件处理
 * 4. 搜索功能
 * 5. 快速操作
 */
internal class MessagingReducerHandler
@Inject
constructor(
    private val conversationIdManager: ConversationIdManager
) {
    fun handle(
        intent: AiCoachContract.Intent,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        when (intent) {
            is AiCoachContract.Intent.SendMessage -> handleSendMessage(intent, currentState)
            is AiCoachContract.Intent.UpdateInput -> handleUpdateInput(intent, currentState)
            is AiCoachContract.Intent.LoadInitialData -> handleLoadInitialData(intent, currentState)
            is AiCoachContract.Intent.FocusInput -> handleFocusInput(intent, currentState)
            is AiCoachContract.Intent.BlurInput -> handleBlurInput(intent, currentState)
            is AiCoachContract.Intent.ClearInput -> handleClearInput(intent, currentState)
            is AiCoachContract.Intent.AttachFiles -> handleAttachFiles(intent, currentState)
            is AiCoachContract.Intent.RemoveAttachment -> handleRemoveAttachment(intent, currentState)
            is AiCoachContract.Intent.UpdateUploadProgress -> handleUpdateUploadProgress(
                intent,
                currentState,
            )
            is AiCoachContract.Intent.StartVoiceInput -> handleStartVoiceInput(intent, currentState)
            is AiCoachContract.Intent.StopVoiceInput -> handleStopVoiceInput(intent, currentState)
            is AiCoachContract.Intent.ToggleToolPicker -> handleToggleToolPicker(intent, currentState)
            is AiCoachContract.Intent.OnVoiceTranscript -> handleOnVoiceTranscript(intent, currentState)
            is AiCoachContract.Intent.SelectImages -> handleSelectImages(intent, currentState)
            is AiCoachContract.Intent.RemoveImage -> handleRemoveImage(intent, currentState)
            is AiCoachContract.Intent.SearchMessages -> handleSearchMessages(intent, currentState)
            is AiCoachContract.Intent.SearchResultsLoadedResult ->
                handleSearchResultsLoaded(
                    intent,
                    currentState,
                )

            is AiCoachContract.Intent.SearchFailedResult -> handleSearchFailed(intent, currentState)
            is AiCoachContract.Intent.ClearSearchResults -> handleClearSearchResults(intent, currentState)
            is AiCoachContract.Intent.LoadQuickActionCategories ->
                handleLoadQuickActionCategories(
                    intent,
                    currentState,
                )
            is AiCoachContract.Intent.LoadActionContext -> handleLoadActionContext(intent, currentState)
            is AiCoachContract.Intent.ToggleQuickActionPanel ->
                handleToggleQuickActionPanel(
                    intent,
                    currentState,
                )
            is AiCoachContract.Intent.QuickActionCategoriesLoadedResult ->
                handleQuickActionCategoriesLoaded(
                    intent,
                    currentState,
                )

            is AiCoachContract.Intent.ActionContextLoadedResult ->
                handleActionContextLoaded(
                    intent,
                    currentState,
                )
            is AiCoachContract.Intent.OnCardClick -> handleOnCardClick(intent, currentState)
            is AiCoachContract.Intent.DismissPanel -> handleDismissPanel(intent, currentState)
            is AiCoachContract.Intent.PreFillInput -> handlePreFillInput(intent, currentState)
            is AiCoachContract.Intent.SetPanelState -> handleSetPanelState(intent, currentState)
            else -> ReduceResult.stateOnly(currentState)
        }

    private fun handleSendMessage(
        intent: AiCoachContract.Intent.SendMessage,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        // 🔥 TOKEN-FLOW 日志：SendMessage Intent 开始处理
        Timber.tag(
            "TOKEN-FLOW",
        ).d(
            "📤 [MessagingReducerHandler] handleSendMessage开始: content='${intent.content.take(
                50,
            )}${if (intent.content.length > 50) "..." else ""}'",
        )
        Timber.d("📤 发送消息: ${intent.content}")

        // 🔥 运行简化的消息发送诊断
        Timber.d("🔍 [消息发送诊断] 开始验证发送条件:")
        Timber.d("  - 输入内容: '${intent.content.take(50)}${if (intent.content.length > 50) "..." else ""}'")
        Timber.d("  - 当前会话: ${currentState.activeSession?.id ?: "null"}")
        Timber.d("  - 流式状态: ${currentState.streamingState::class.simpleName}")
        Timber.d("  - 是否加载中: ${currentState.isLoading}")
        Timber.d("  - canSendMessage: ${currentState.canSendMessage}")

        // 🔥 【架构重构】检查发送条件，使用 StreamingState 进行精确判断
        val hasText = intent.content.isNotBlank()
        val notStreaming = currentState.streamingState is AiCoachContract.StreamingState.Idle
        val notLoading = !currentState.isLoading
        val sessionExists = currentState.activeSession != null

        // 🔥 TOKEN-FLOW 日志：条件检查结果
        Timber.tag(
            "TOKEN-FLOW",
        ).d(
            "🔍 [MessagingReducerHandler] 发送条件检查: hasText=$hasText, notStreaming=$notStreaming, notLoading=$notLoading, sessionExists=$sessionExists, streamingState=${currentState.streamingState::class.simpleName}",
        )

        if (!hasText) {
            // 🔥 TOKEN-FLOW 日志：消息内容为空
            Timber.tag("TOKEN-FLOW").e("❌ [MessagingReducerHandler] 消息内容为空，取消发送")
            Timber.e("❌ 消息内容为空，取消发送")
            return ReduceResult.stateOnly(currentState)
        }

        if (!notStreaming) {
            // 🔥 TOKEN-FLOW 日志：正在流式响应中
            Timber.tag("TOKEN-FLOW").e("❌ [MessagingReducerHandler] 正在流式响应中，取消发送")
            Timber.e("❌ 正在流式响应中，取消发送")
            return ReduceResult.stateOnly(currentState)
        }

        if (!notLoading) {
            // 🔥 TOKEN-FLOW 日志：正在加载中
            Timber.tag("TOKEN-FLOW").e("❌ [MessagingReducerHandler] 正在加载中，取消发送")
            Timber.e("❌ 正在加载中，取消发送")
            return ReduceResult.stateOnly(currentState)
        }

        // 🔥 【关键修复】如果没有会话，自动创建会话而不是阻止发送
        if (!sessionExists) {
            // 🔥 TOKEN-FLOW 日志：没有活跃会话，创建新会话
            Timber.tag("TOKEN-FLOW").w("⚠️ [MessagingReducerHandler] 没有活跃会话，自动创建新会话并保存待发消息")
            Timber.w("⚠️ 没有活跃会话，自动创建新会话并保存待发消息")

            // 保存待发消息到状态中
            val newState = currentState.copy(
                pendingMessage = intent.content,
                isLoading = true,
                // 🔥 【架构重构】设置流式状态为等待第一个token
                streamingState = AiCoachContract.StreamingState.AwaitingFirstToken,
            )

            // 触发创建会话Effect
            val effects = listOf(AiCoachContract.Effect.CreateNewSession)

            // 🔥 TOKEN-FLOW 日志：返回创建会话Effect
            Timber.tag("TOKEN-FLOW").d("🔄 [MessagingReducerHandler] 返回CreateNewSession Effect，待发消息已保存")
            return ReduceResult.withEffects(newState, effects)
        }

        if (intent.content.isBlank()) {
            Timber.e("❌ 消息内容为空，取消发送")
            return ReduceResult.stateOnly(currentState)
        }

        // 🔥 【多轮对话调试】详细记录发送消息时的状态
        Timber.d("🔍 [多轮对话调试] SendMessage开始处理:")
        Timber.d("🔍   - 当前消息数: ${currentState.messages.size}")
        Timber.d("🔍   - streamingState: ${currentState.streamingState::class.simpleName}")

        // 🔥 【关键调试】记录当前所有消息的状态
        currentState.messages.forEachIndexed { index, msg ->
            Timber.d(
                "🔍 [当前消息$index] id=${msg.id}, isFromUser=${msg.isFromUser}, saveStatus=${msg.saveStatus}, content=${
                    msg.content.take(
                        30,
                    )
                }...",
            )
        }

        // 🔥 修复：改善会话ID获取逻辑，提供备选方案
        val sessionId = currentState.activeSessionId ?: "default_session"

        // 🔥 增加详细日志来追踪会话状态
        Timber.d("🔍 会话状态诊断:")
        Timber.d("  - activeSession: ${currentState.activeSession}")
        Timber.d("  - activeSessionId: ${currentState.activeSessionId}")
        Timber.d("  - 使用的sessionId: $sessionId")
        Timber.d("  - activeSession?.id: ${currentState.activeSession?.id}")
        Timber.d("  - activeSession是否为null: ${currentState.activeSession == null}")

        // 🔥 强化诊断：如果使用默认会话，记录详细信息
        if (sessionId.isEmpty()) {
            Timber.e("🚨 严重错误：sessionId为空字符串！")
            Timber.e("  - currentState.activeSession: ${currentState.activeSession}")
            Timber.e("  - currentState.activeSessionId: ${currentState.activeSessionId}")
            // 使用fallback值
            val fallbackSessionId = "emergency_session_${System.currentTimeMillis()}"
            Timber.w("🛟 使用紧急会话ID: $fallbackSessionId")

            // 即使在紧急情况下也要继续保存消息
            // 🔥 【Plan B重构】创建MessageContext用于用户消息
            val userMessageContext = conversationIdManager.createMessageContext(fallbackSessionId)
            val userMessage =
                createUserMessage(
                    content = intent.content,
                    messageContext = userMessageContext,
                )

            val updatedMessages = (currentState.messages + userMessage).toImmutableList()
            val newState =
                currentState.copy(
                    messages = updatedMessages,
                    inputState = currentState.inputState.copy(text = ""),
                    // 🔥 【架构重构】设置流式状态为等待第一个token
                    streamingState = AiCoachContract.StreamingState.AwaitingFirstToken,
                    // 🔥 【修复冲突】移除ThinkingBox状态重置，由ThinkingBox内部管理
                )

            val effects =
                listOf(
                    AiCoachContract.Effect.SaveUserMessage(
                        sessionId = fallbackSessionId,
                        messageId = userMessage.id,
                        content = intent.content,
                    ),
                )

            Timber.w("⚠️ 使用紧急会话保存消息: sessionId=$fallbackSessionId, userMessageId=${userMessage.id}")
            return ReduceResult.withEffects(newState, effects)
        }

        if (sessionId == "default_session") {
            Timber.w("⚠️ 使用默认会话ID，可能存在会话管理问题")
            Timber.w("  - 这通常意味着会话初始化失败或SessionCreated Intent未正确处理")
            Timber.w("  - 继续使用default_session以确保消息能被保存")
        }

        // 🔥 【Plan B重构】使用ConversationIdManager创建消息上下文
        val userMessageContext = conversationIdManager.createMessageContext(sessionId)
        val aiMessageContext = conversationIdManager.createMessageContext(sessionId)

        Timber.i("🆕 创建用户消息: ${userMessageContext.getDisplayId()} in session $sessionId")
        Timber.i("🆕 创建AI消息: ${aiMessageContext.getDisplayId()} in session $sessionId")

        // 将用户消息添加到消息列表
        val userMessage =
            createUserMessage(
                content = intent.content,
                messageContext = userMessageContext,
            )

        Timber.d("✅ 用户消息创建成功: ${userMessageContext.getDisplayId()}, sessionId=$sessionId")

        // 🔥 【Primary Fix】创建AI消息占位符，确保保护逻辑有内容可保护
        val aiMessagePlaceholder =
            createAiMessage(
                messageContext = aiMessageContext,
                content = "",
            )

        Timber.d("✅ AI消息占位符创建成功: ${aiMessageContext.getDisplayId()}")

        // 🔥 【Primary Fix】同时添加用户消息和AI占位符到状态
        val updatedMessages = (currentState.messages + userMessage + aiMessagePlaceholder).toImmutableList()

        // 🔥 【多轮对话调试】记录消息添加后的状态
        Timber.d("🔍 [多轮对话调试] 消息添加完成:")
        Timber.d("🔍   - 原消息数: ${currentState.messages.size}")
        Timber.d("🔍   - 新消息数: ${updatedMessages.size}")
        Timber.d("🔍   - 用户消息ID: ${userMessage.id}")
        Timber.d("🔍   - AI占位符ID: ${aiMessagePlaceholder.id}")

        // 🔥 【关键调试】记录更新后所有消息的状态
        updatedMessages.takeLast(5).forEachIndexed { index, msg ->
            Timber.d(
                "🔍 [更新后消息${updatedMessages.size - 5 + index}] id=${msg.id}, isFromUser=${msg.isFromUser}, saveStatus=${msg.saveStatus}",
            )
        }

        val newState =
            currentState.copy(
                messages = updatedMessages,
                inputState = currentState.inputState.copy(text = ""),
                // 🔥 【架构重构】设置流式状态为等待第一个token
                streamingState = AiCoachContract.StreamingState.AwaitingFirstToken,
                // 🔥 【修复冲突】移除ThinkingBox状态重置，由ThinkingBox内部管理
            )

        Timber.d("🔍 [多轮对话调试] 新状态创建完成: streamingState=${newState.streamingState::class.simpleName}")

        // 🔥 【Plan B重构】生成优化的Effect，使用MessageContext
        val saveUserMessageEffect =
            AiCoachContract.Effect.SaveUserMessage(
                sessionId = sessionId,
                messageId = userMessageContext.messageId, // 🔥 【Plan B重构】使用messageId参数
                content = intent.content,
            )

        Timber.d(
            "🚀 [REDUCER-DEBUG] 生成 SaveUserMessage Effect: sessionId=$sessionId, messageId=${userMessageContext.getDisplayId()}",
        )
        Timber.d(
            "🚀 [REDUCER-DEBUG] Effect 内容: ${intent.content.take(
                100,
            )}${if (intent.content.length > 100) "..." else ""}",
        )

        val effects =
            listOf(
                // 🔥 Sprint-2: 先触发记忆召回
                AiCoachContract.Effect.RecallMemoriesFromService(
                    userId = currentState.activeSession?.userId ?: "default",
                    query = intent.content,
                    tokenBudget = 600,
                ),
                // 1. 保存用户消息到数据库
                saveUserMessageEffect,
                // 2. 启动AI流式响应 - 使用MessageContext
                AiCoachContract.Effect.StartAiStream(
                    messageContext = aiMessageContext, // 🔥 【Plan B重构】使用完整的MessageContext
                    prompt = intent.content,
                    sessionMessages = emptyList() // TODO: 从当前会话获取历史消息
                ),
                // 🔥 【Plan B架构】移除LaunchThinkingBoxDisplay - ThinkingBox被动响应
            )

        // 🔥 【Plan B架构】TOKEN-FLOW 日志：Effects 生成完成
        Timber.tag(
            "TOKEN-FLOW",
        ).d(
            "🚀 [MessagingReducerHandler] 生成${effects.size}个Effects: RecallMemories + SaveUserMessage + StartAiStream",
        )
        Timber.tag(
            "TOKEN-FLOW",
        ).d(
            "🎯 [MessagingReducerHandler] StartAiStream Effect: sessionId=$sessionId, messageId=${aiMessageContext.messageId}",
        )
        Timber.d("🚀 [REDUCER-DEBUG] 总共生成 ${effects.size} 个 Effects")

        Timber.d("✅ SendMessage Effect已生成: RecallMemories + SaveUserMessage + StartAiStream")
        Timber.d(
            "🔍 SaveUserMessage Effect详情: sessionId=$sessionId, messageId=${userMessage.id}, content.length=${intent.content.length}",
        )
        Timber.d("🔍 Effects总数: ${effects.size}")
        Timber.d("🔍 Effects类型: ${effects.map { it::class.simpleName }}")

        return ReduceResult.withEffects(newState, effects)
    }

    private fun handleUpdateInput(
        intent: AiCoachContract.Intent.UpdateInput,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        Timber.d("🔄 更新输入: ${intent.text}")

        return ReduceResult.stateOnly(
            currentState.copy(
                inputState = currentState.inputState.copy(text = intent.text),
            ),
        )
    }

    private fun handleLoadInitialData(
        intent: AiCoachContract.Intent.LoadInitialData,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        // 🔥 临时修复：不设置isLoading=true，避免无限循环
        // 因为Effect处理被禁用，isLoading永远不会被设置回false
        val newState = currentState.copy(isLoading = false)

        // 🔥 暂时不发出Effect，避免循环
        val effects = emptyList<AiCoachContract.Effect>()

        return ReduceResult.withEffects(newState, effects)
    }

    private fun handleFocusInput(
        intent: AiCoachContract.Intent.FocusInput,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState = currentState.inputState.copy(isFocused = true),
            ),
        )

    private fun handleBlurInput(
        intent: AiCoachContract.Intent.BlurInput,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState = currentState.inputState.copy(isFocused = false),
            ),
        )

    private fun handleClearInput(
        intent: AiCoachContract.Intent.ClearInput,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState =
                currentState.inputState.copy(
                    text = "",
                    selectedImages = kotlinx.collections.immutable.persistentListOf(),
                ),
            ),
        )

    private fun handleAttachFiles(
        intent: AiCoachContract.Intent.AttachFiles,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        intent.uris.map { uri ->
            AiCoachContract.Attachment(
                type = AiCoachContract.AttachmentType.IMAGE,
                uri = uri,
                name = "image_${System.currentTimeMillis()}.jpg",
                size = 0L,
                uploadProgress = 0f,
            )
        }

        return ReduceResult.stateOnly(
            currentState.copy(
                inputState =
                currentState.inputState.copy(
                    selectedImages =
                    (
                        currentState.inputState.selectedImages +
                            intent.uris.map {
                                it.toString()
                            }
                        ).toImmutableList(),
                ),
            ),
        )
    }

    private fun handleRemoveAttachment(
        intent: AiCoachContract.Intent.RemoveAttachment,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        val updatedAttachments =
            currentState.inputState.selectedImages
                .filterIndexed { index, _ -> index != intent.index }
                .toImmutableList()

        return ReduceResult.stateOnly(
            currentState.copy(
                inputState =
                currentState.inputState.copy(
                    selectedImages = updatedAttachments,
                ),
            ),
        )
    }

    private fun handleUpdateUploadProgress(
        intent: AiCoachContract.Intent.UpdateUploadProgress,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        // 简化处理，因为 attachedImages 是 Uri 列表，不包含进度信息
        return ReduceResult.stateOnly(currentState)
    }

    private fun handleStartVoiceInput(
        intent: AiCoachContract.Intent.StartVoiceInput,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState = currentState.inputState.copy(voiceRecording = true),
            ),
        )

    private fun handleStopVoiceInput(
        intent: AiCoachContract.Intent.StopVoiceInput,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState = currentState.inputState.copy(voiceRecording = false),
            ),
        )

    private fun handleToggleToolPicker(
        intent: AiCoachContract.Intent.ToggleToolPicker,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState =
                currentState.inputState.copy(
                    isToolbarExpanded = !currentState.inputState.isToolbarExpanded,
                ),
            ),
        )

    private fun handleOnVoiceTranscript(
        intent: AiCoachContract.Intent.OnVoiceTranscript,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState =
                currentState.inputState.copy(
                    voiceTranscript = intent.partialText,
                ),
            ),
        )

    private fun handleSelectImages(
        intent: AiCoachContract.Intent.SelectImages,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState =
                currentState.inputState.copy(
                    pendingImages = intent.uris.toImmutableList(),
                ),
            ),
        )

    private fun handleRemoveImage(
        intent: AiCoachContract.Intent.RemoveImage,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        val updatedImages =
            currentState.inputState.pendingImages
                .filter { it != intent.uri }
                .toImmutableList()

        return ReduceResult.stateOnly(
            currentState.copy(
                inputState =
                currentState.inputState.copy(
                    pendingImages = updatedImages,
                ),
            ),
        )
    }

    private fun handleSearchMessages(
        intent: AiCoachContract.Intent.SearchMessages,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        val newState =
            currentState.copy(
                searchState =
                currentState.searchState.copy(
                    query = intent.query,
                    isSearching = true,
                ),
            )

        val effect = AiCoachContract.Effect.SearchMessages(intent.query, intent.sessionId)

        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleSearchResultsLoaded(
        intent: AiCoachContract.Intent.SearchResultsLoadedResult,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                searchState =
                currentState.searchState.copy(
                    results = intent.results,
                    isSearching = false,
                    hasSearched = true,
                ),
            ),
        )

    private fun handleSearchFailed(
        intent: AiCoachContract.Intent.SearchFailedResult,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                searchState =
                currentState.searchState.copy(
                    searchError = intent.error,
                    isSearching = false,
                ),
            ),
        )

    private fun handleClearSearchResults(
        intent: AiCoachContract.Intent.ClearSearchResults,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                searchState = AiCoachContract.SearchState(),
            ),
        )

    private fun handleLoadQuickActionCategories(
        intent: AiCoachContract.Intent.LoadQuickActionCategories,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        val effect = AiCoachContract.Effect.LoadQuickActionCategories

        return ReduceResult.withEffect(currentState, effect)
    }

    private fun handleLoadActionContext(
        intent: AiCoachContract.Intent.LoadActionContext,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        val newState =
            currentState.copy(
                quickActionState = currentState.quickActionState.copy(isLoading = true),
            )

        Timber.w("⚠️ LoadActionContext功能暂未实现")
        return ReduceResult.stateOnly(newState)
    }

    private fun handleToggleQuickActionPanel(
        intent: AiCoachContract.Intent.ToggleQuickActionPanel,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(isPanelVisible = !currentState.isPanelVisible),
        )

    private fun handleQuickActionCategoriesLoaded(
        intent: AiCoachContract.Intent.QuickActionCategoriesLoadedResult,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                quickActionState =
                currentState.quickActionState.copy(
                    categories = intent.categories.toImmutableList(),
                    isLoading = false,
                ),
            ),
        )

    private fun handleActionContextLoaded(
        intent: AiCoachContract.Intent.ActionContextLoadedResult,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                quickActionState =
                currentState.quickActionState.copy(
                    selectedAction = intent.action,
                    isLoading = false,
                ),
            ),
        )

    private fun handleOnCardClick(
        intent: AiCoachContract.Intent.OnCardClick,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> {
        // 找到点击的卡片并展开
        val clickedCard = currentState.cards.find { it == intent.cardId }

        return ReduceResult.stateOnly(
            currentState.copy(
                expandedCard = clickedCard,
                isPanelVisible = true,
            ),
        )
    }

    private fun handleDismissPanel(
        intent: AiCoachContract.Intent.DismissPanel,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                expandedCard = null,
                isPanelVisible = false,
            ),
        )

    private fun handlePreFillInput(
        intent: AiCoachContract.Intent.PreFillInput,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(
                inputState = currentState.inputState.copy(text = intent.text),
            ),
        )

    private fun handleSetPanelState(
        intent: AiCoachContract.Intent.SetPanelState,
        currentState: AiCoachContract.State,
    ): ReduceResult<AiCoachContract.State, AiCoachContract.Effect> =
        ReduceResult.stateOnly(
            currentState.copy(panelState = intent.state),
        )

    // ===== 消息创建工具方法 (合并自MessageMapper) =====

    /**
     * 创建用户消息 - Plan B重构版本
     * 🔥 【Plan B重构】使用MessageContext统一ID管理
     */
    private fun createUserMessage(
        content: String,
        messageContext: ConversationIdManager.MessageContext,
    ): AiCoachContract.MessageUi =
        AiCoachContract.MessageUi(
            id = messageContext.messageId,
            content = content,
            isFromUser = true,
            timestamp = Clock.System.now(),
        )

    /**
     * 创建AI消息 - Plan B重构版本
     * 🔥 【Plan B重构】使用MessageContext统一ID管理，流式状态由 StreamingState 统一管理
     */
    private fun createAiMessage(
        messageContext: ConversationIdManager.MessageContext,
        content: String = "",
    ): AiCoachContract.MessageUi =
        AiCoachContract.MessageUi(
            id = messageContext.messageId,
            content = content,
            isFromUser = false,
            timestamp = Clock.System.now(),
            saveStatus = AiCoachContract.SaveStatus.PENDING, // 🔥 【Primary Fix】设置保存状态
        )

    /**
     * 生成消息ID
     * 🔥 修复：统一使用UUID确保绝对唯一性，避免时间戳冲突
     */
    private fun generateMessageId(): String {
        val messageId = com.example.gymbro.core.util.Constants.MessageId.generate()

        // 🔥 【数据流验证】记录 Coach 生成 messageId
        // TODO: 注入 DataFlowValidator 并调用 validateCoachMessageGeneration
        Timber.tag(GymBroLogTags.Coach.MESSAGING).d("🔍 [数据流] Coach生成messageId: $messageId")

        return messageId
    }
}
